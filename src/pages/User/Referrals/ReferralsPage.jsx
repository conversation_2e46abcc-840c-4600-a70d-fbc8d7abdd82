import React, { useState, useEffect, useCallback } from "react";
import { useNavigate, Link, useLocation, useSearchParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { SkeletonLoader } from "Components/Skeleton";
import ReferralFormModal from "Components/Modals/ReferralFormModal";
import { showToast } from "Context/Global";
import { GlobalContext } from "Context/Global";
import { useContext } from "react";
import ConfirmationModal from "Components/ConfirmationModal/ConfirmationModal";
import RecommendationsPage from "../Recommendations/RecommendationsPage";
import debounce from "lodash/debounce";
import { useNotificationTrigger } from "../../../hooks/useNotificationTrigger";


const formatDate = (dateString) => {
  const date = new Date(dateString);
  const day = date.getDate();
  const month = date.toLocaleString("default", { month: "short" });
  const year = date.getFullYear();
  return `${day} ${month}, ${year}`;
};

const FlashIcon = () => (
  <svg
    width="11"
    height="12"
    viewBox="0 0 15 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_3_1374)">
      <path
        d="M11.8253 1.39354C12.0097 0.965419 11.8722 0.465419 11.4941 0.190419C11.1159 -0.0845806 10.6003 -0.0595806 10.2472 0.246669L2.24719 7.24667C1.93469 7.52167 1.82219 7.96229 1.96907 8.34979C2.11594 8.73729 2.49094 8.99979 2.90657 8.99979H6.39094L3.98782 14.606C3.80344 15.0342 3.94094 15.5342 4.31907 15.8092C4.69719 16.0842 5.21282 16.0592 5.56594 15.7529L13.5659 8.75292C13.8784 8.47792 13.9909 8.03729 13.8441 7.64979C13.6972 7.26229 13.3253 7.00292 12.9066 7.00292H9.42219L11.8253 1.39354Z"
        fill="#7DD87D"
      />
    </g>
  </svg>
);

const OpenReferralIcon = () => (
  <svg
    width="13"
    height="12"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_3_1393)">
      <path
        d="M11.5 8C11.5 8.69375 11.4625 9.3625 11.3969 10H5.60313C5.53438 9.3625 5.5 8.69375 5.5 8C5.5 7.30625 5.5375 6.6375 5.60313 6H11.3969C11.4656 6.6375 11.5 7.30625 11.5 8ZM12.4 6H16.2469C16.4125 6.64062 16.5 7.30937 16.5 8C16.5 8.69063 16.4125 9.35938 16.2469 10H12.4C12.4656 9.35625 12.5 8.6875 12.5 8C12.5 7.3125 12.4656 6.64375 12.4 6ZM15.9187 5H12.2719C11.9594 3.00312 11.3406 1.33125 10.5437 0.2625C12.9906 0.909375 14.9812 2.68438 15.9156 5H15.9187ZM11.2594 5H5.74062C5.93125 3.8625 6.225 2.85625 6.58437 2.04063C6.9125 1.30313 7.27812 0.76875 7.63125 0.43125C7.98125 0.1 8.27187 0 8.5 0C8.72812 0 9.01875 0.1 9.36875 0.43125C9.72187 0.76875 10.0875 1.30313 10.4156 2.04063C10.7781 2.85313 11.0687 3.85938 11.2594 5ZM4.72813 5H1.08125C2.01875 2.68438 4.00625 0.909375 6.45625 0.2625C5.65938 1.33125 5.04063 3.00312 4.72813 5ZM0.753125 6H4.6C4.53437 6.64375 4.5 7.3125 4.5 8C4.5 8.6875 4.53437 9.35625 4.6 10H0.753125C0.5875 9.35938 0.5 8.69063 0.5 8C0.5 7.30937 0.5875 6.64062 0.753125 6ZM6.58437 13.9563C6.22187 13.1438 5.93125 12.1375 5.74062 11H11.2594C11.0687 12.1375 10.775 13.1438 10.4156 13.9563C10.0875 14.6938 9.72187 15.2281 9.36875 15.5656C9.01875 15.9 8.72812 16 8.5 16C8.27187 16 7.98125 15.9 7.63125 15.5688C7.27812 15.2313 6.9125 14.6969 6.58437 13.9594V13.9563ZM4.72813 11C5.04063 12.9969 5.65938 14.6687 6.45625 15.7375C4.00625 15.0906 2.01875 13.3156 1.08125 11H4.72813ZM15.9187 11C14.9812 13.3156 12.9937 15.0906 10.5469 15.7375C11.3438 14.6687 11.9594 12.9969 12.275 11H15.9187Z"
        fill="#7DD87D"
      />
    </g>
  </svg>
);

const RepostedIcon = () => (
  <svg
    width="15"
    height="8"
    viewBox="0 0 19 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.99998 10.9999C9.5531 10.9999 9.99998 10.553 9.99998 9.9999C9.99998 9.44678 9.5531 8.9999 8.99998 8.9999H5.49998C4.94685 8.9999 4.49998 8.55303 4.49998 7.9999V3.9999H5.49998C5.9031 3.9999 6.26873 3.75615 6.42498 3.38115C6.58123 3.00615 6.49373 2.57803 6.20935 2.29053L4.20935 0.290527C3.81873 -0.100098 3.18435 -0.100098 2.79373 0.290527L0.793727 2.29053C0.506227 2.57803 0.421852 3.00615 0.578102 3.38115C0.734352 3.75615 1.09685 3.9999 1.5031 3.9999H2.5031V7.9999C2.5031 9.65615 3.84685 10.9999 5.5031 10.9999H8.99998ZM9.99998 0.999902C9.44685 0.999902 8.99998 1.44678 8.99998 1.9999C8.99998 2.55303 9.44685 2.9999 9.99998 2.9999H13.5C14.0531 2.9999 14.5 3.44678 14.5 3.9999V7.9999H13.5C13.0969 7.9999 12.7312 8.24365 12.575 8.61865C12.4187 8.99365 12.5062 9.42178 12.7906 9.70928L14.7906 11.7093C15.1812 12.0999 15.8156 12.0999 16.2062 11.7093L18.2062 9.70928C18.4937 9.42178 18.5781 8.99365 18.4219 8.61865C18.2656 8.24365 17.9031 7.9999 17.4969 7.9999H16.4969V3.9999C16.4969 2.34365 15.1531 0.999902 13.4969 0.999902H9.99998Z"
      fill="#EAEAEA"
    />
  </svg>
);

const getInitials = (name) => {
  return name
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);
};

const UserAvatar = ({ user }) => {
  if (user.photo?.value) {
    return (
      <img
        src={user.photo.value}
        alt={user.name.value}
        className="object-cover w-8 h-8 rounded-full"
      />
    );
  }

  return (
    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white">
      {getInitials(user.name.value)}
    </div>
  );
};

const ReferralRecommendForm = ({ referralId, onClose }) => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  // User search states
  const [suggestedUsers, setSuggestedUsers] = useState([]);
  const [searchingUsers, setSearchingUsers] = useState(false);
  const [activeSearchField, setActiveSearchField] = useState(null); // Track which field is being searched
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [visibleReferrals, setVisibleReferrals] = useState(1); // Track how many referral forms are visible

  // Function to add more referral forms
  const addMoreReferrals = () => {
    if (visibleReferrals < 5) {
      setVisibleReferrals(visibleReferrals + 1);
    }
  };

  // Function to remove referral forms
  const removeReferral = () => {
    if (visibleReferrals > 1) {
      setVisibleReferrals(visibleReferrals - 1);
      // Clear the data for the removed referral
      const indexToRemove = visibleReferrals - 1;
      setFormData((prev) => ({
        ...prev,
        recommendation: {
          value: prev.recommendation.value.map((rec, idx) =>
            idx === indexToRemove ? { first_name: "", last_name: "", user_id: "" } : rec
          ),
        },
      }));
    }
  };

  const [formData, setFormData] = useState({
    first_name: { value: "" },
    last_name: { value: "" },
    recommendation: {
      value: [
        {
          first_name: "",
          last_name: "",
          user_id: "12",
        },
        {
          first_name: "",
          last_name: "",
          user_id: "12",
        },
        {
          first_name: "",
          last_name: "",
          user_id: "",
        },
        {
          first_name: "",
          last_name: "",
          user_id: "",
        },
        {
          first_name: "",
          last_name: "",
          user_id: "",
        },
      ],
    },
  });

  // Add useEffect to fetch user details
  useEffect(() => {
    const fetchUserDetails = async () => {
      try {
        const sdk = new MkdSDK();
        const response = await sdk.callRawAPI(
          "/v1/api/dealmaker/user/details",
          {},
          "GET"
        );

        if (!response.error && response.model) {
          setFormData(prev => ({
            ...prev,
            first_name: { value: response.model.first_name.value || "" },
            last_name: { value: response.model.last_name.value || "" }
          }));
        }
      } catch (err) {
        console.error("Failed to fetch user details:", err);
        setError("Failed to fetch user details");
      }
    };

    fetchUserDetails();
  }, []);

  // Debounced search function for users
  const debouncedUserSearch = useCallback(
    debounce(async (query) => {
      if (query.trim().length < 2) {
        setSuggestedUsers([]);
        setShowSuggestions(false);
        return;
      }

      try {
        setSearchingUsers(true);
        const sdk = new MkdSDK();
        const response = await sdk.SearchUsers(query);

        if (!response.error) {
          setSuggestedUsers(response.list || []);
          setShowSuggestions(true);
        }
      } catch (err) {
        console.error("Failed to search users:", err);
      } finally {
        setSearchingUsers(false);
      }
    }, 300),
    []
  );

  // Handle user selection from suggestions
  const handleUserSelect = (user, fieldIndex) => {
    const firstName = user.name.value ? user.name.value.split(' ')[0] : '';
    const lastName = user.name.value ? user.name.value.split(' ').slice(1).join(' ') : '';

    setFormData((prev) => ({
      ...prev,
      recommendation: {
        value: prev.recommendation.value.map((rec, idx) =>
          idx === fieldIndex ? {
            ...rec,
            first_name: firstName,
            last_name: lastName,
            user_id: user.id.value.toString()
          } : rec
        ),
      },
    }));

    setShowSuggestions(false);
    setActiveSearchField(null);
    setSuggestedUsers([]);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showSuggestions && !event.target.closest('.relative')) {
        setShowSuggestions(false);
        setActiveSearchField(null);
        setSuggestedUsers([]);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSuggestions]);

  console.log("formData", formData);



  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Get all non-empty recommendations (only require first_name now)
    const validRecommendations = formData.recommendation.value.filter(
      (rec) => rec.first_name.trim() !== ""
    );

    // If no valid recommendations, show error
    if (validRecommendations.length === 0) {
      setError("Please fill in at least one recommendation");
      setLoading(false);
      return;
    }

    try {
      const sdk = new MkdSDK();

      // Create payload with proper structure - wrapping everything in value objects
      const payload = {
        first_name: { value: formData.first_name.value },
        last_name: { value: formData.last_name.value },
        email: { value: formData.email?.value || "" },
        online_accounts: { value: [] },
        recommendation: {
          value: validRecommendations.map((rec) => ({
            first_name: { value: rec.first_name },
            last_name: { value: rec.last_name },
            email: { value: rec.email || "" },
            user_id: { value: rec.user_id || "" },
          })),
        },
      };

      console.log("Submitting payload:", payload); // For debugging

      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/referral/${referralId}/recommend`,
        payload,
        "POST"
      );

      if (!response.error) {
        showToast(
          globalDispatch,
          "Referral recommendation submitted successfully!",
          5000,
          "success"
        );
        onClose();
      } else {
        setError(response.message);
      }
    } catch (err) {
      setError(err.message || "Failed to submit referral recommendation");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      style={{
        width: "700px",
        backgroundColor: "#161616",
        border: "1px solid #363636",
        margin: "auto",
        borderRadius: "10px",
        padding: "20px",
      }}
      className="mb-6 rounded-lg border border-[#363636] bg-[#1e1e1e] p-6"
    >
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Your Info Section */}
        <div>
          <h2 className="mb-4 text-xl font-semibold text-[#eaeaea]">
            Your Name *
          </h2>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">
                  First Name
                </label>
                <input
                  type="text"
                  value={formData.first_name.value}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      first_name: { value: e.target.value },
                    }))
                  }
                  className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                  required
                />
              </div>
              <div>
                <label className="mb-2 block text-sm text-[#b5b5b5]">
                  Last Name
                </label>
                <input
                  type="text"
                  value={formData.last_name.value}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      last_name: { value: e.target.value },
                    }))
                  }
                  className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Recommendation #1 */}
        <div className="relative">
          <h2 className="mb-4 text-xl font-semibold text-[#eaeaea]">
          Referral #1 *
          </h2>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <label className="mb-2 block text-sm text-[#b5b5b5]">
                First Name
              </label>
              <input
                type="text"
                value={formData.recommendation.value[0].first_name}
                onChange={(e) => {
                  const value = e.target.value;
                  setFormData((prev) => ({
                    ...prev,
                    recommendation: {
                      value: prev.recommendation.value.map((rec, idx) =>
                        idx === 0 ? { ...rec, first_name: value } : rec
                      ),
                    },
                  }));
                  setActiveSearchField(`first_name_0`);
                  debouncedUserSearch(value);
                }}
                onFocus={() => setActiveSearchField(`first_name_0`)}
                className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                required
              />
            </div>
            <div className="relative">
              <label className="mb-2 block text-sm text-[#b5b5b5]">
                Last Name
              </label>
              <input
                type="text"
                value={formData.recommendation.value[0].last_name}
                onChange={(e) => {
                  const value = e.target.value;
                  setFormData((prev) => ({
                    ...prev,
                    recommendation: {
                      value: prev.recommendation.value.map((rec, idx) =>
                        idx === 0 ? { ...rec, last_name: value } : rec
                      ),
                    },
                  }));
                  setActiveSearchField(`last_name_0`);
                  debouncedUserSearch(value);
                }}
                onFocus={() => setActiveSearchField(`last_name_0`)}
                className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
              />
            </div>
          </div>

          {/* User suggestions dropdown for Referral #1 */}
          {showSuggestions && activeSearchField && activeSearchField.includes('_0') && (
            <div className="absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] py-2 shadow-lg">
              {searchingUsers ? (
                <div className="px-4 py-2 text-[#b5b5b5]">Searching...</div>
              ) : suggestedUsers.length > 0 ? (
                suggestedUsers.map((user) => (
                  <div
                    key={user.id.value}
                    onClick={() => handleUserSelect(user, 0)}
                    className="cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#2e7d32]"
                  >
                    <div className="font-medium">{user.name.value || 'No name'}</div>
                  </div>
                ))
              ) : (
                <div className="px-4 py-2 text-[#b5b5b5]">No users found</div>
              )}
            </div>
          )}
        </div>

        {/* Dynamic Referral Forms */}
        {Array.from({ length: visibleReferrals - 1 }, (_, index) => {
          const referralIndex = index + 1;
          return (
            <div key={referralIndex} className="relative">
              <h2 className="mb-4 text-xl font-semibold text-[#eaeaea]">
                Referral #{referralIndex + 1}
              </h2>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="relative">
                  <label className="mb-2 block text-sm text-[#b5b5b5]">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={formData.recommendation.value[referralIndex].first_name}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        recommendation: {
                          value: prev.recommendation.value.map((rec, idx) =>
                            idx === referralIndex ? { ...rec, first_name: value } : rec
                          ),
                        },
                      }));
                      setActiveSearchField(`first_name_${referralIndex}`);
                      debouncedUserSearch(value);
                    }}
                    onFocus={() => setActiveSearchField(`first_name_${referralIndex}`)}
                    className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                    required
                  />
                </div>
                <div className="relative">
                  <label className="mb-2 block text-sm text-[#b5b5b5]">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={formData.recommendation.value[referralIndex].last_name}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        recommendation: {
                          value: prev.recommendation.value.map((rec, idx) =>
                            idx === referralIndex ? { ...rec, last_name: value } : rec
                          ),
                        },
                      }));
                      setActiveSearchField(`last_name_${referralIndex}`);
                      debouncedUserSearch(value);
                    }}
                    onFocus={() => setActiveSearchField(`last_name_${referralIndex}`)}
                    className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                  />
                </div>
              </div>

              {/* User suggestions dropdown */}
              {showSuggestions && activeSearchField && activeSearchField.includes(`_${referralIndex}`) && (
                <div className="absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] py-2 shadow-lg">
                  {searchingUsers ? (
                    <div className="px-4 py-2 text-[#b5b5b5]">Searching...</div>
                  ) : suggestedUsers.length > 0 ? (
                    suggestedUsers.map((user) => (
                      <div
                        key={user.id.value}
                        onClick={() => handleUserSelect(user, referralIndex)}
                        className="cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#2e7d32]"
                      >
                        <div className="font-medium">{user.name.value || 'No name'}</div>
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-[#b5b5b5]">No users found</div>
                  )}
                </div>
              )}
            </div>
          );
        })}

        {/* Add/Remove Referrals Buttons */}
        <div className="flex justify-center gap-4">
          {visibleReferrals < 5 && (
            <button
              type="button"
              onClick={addMoreReferrals}
              className="rounded-lg border border-[#363636] bg-[#161616] px-6 py-3 text-[#eaeaea] hover:bg-[#242424] transition-colors"
            >
              + Add Another Referral ({visibleReferrals}/5)
            </button>
          )}

          {visibleReferrals > 1 && (
            <button
              type="button"
              onClick={removeReferral}
              className="rounded-lg border border-red-500 bg-red-500/10 px-6 py-3 text-red-400 hover:bg-red-500/20 transition-colors"
            >
              - Remove Last Referral
            </button>
          )}
        </div>



        {error && <p className="text-sm text-red-500">{error}</p>}

        <div className="flex justify-center gap-4 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="rounded-lg bg-[#363636] px-6 py-3 text-sm text-white hover:bg-[#404040]"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="rounded-lg bg-[#2e7d32] px-6 py-3 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50"
          >
            Submit Referral
          </button>
        </div>
      </form>
    </div>
  );
};

const RecommendationModal = ({ isOpen, onClose, recommendations }) => {
  console.log(isOpen, recommendations, onclose);
  if (!isOpen) return null;

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center">
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      <div className="relative z-50 w-1/2 max-w-3xl rounded-lg bg-[#161616] p-6 shadow-xl">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-[#eaeaea]">
            Referrals
          </h3>
          <button
            onClick={onClose}
            className="text-[#b5b5b5] hover:text-[#eaeaea]"
          >
            <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          {recommendations.map((rec, index) => (
            <div
              key={rec.id.value}
              className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4"
            >
              <div className="flex justify-between items-center mb-4">
                <div className="flex gap-3 items-center">
                  <UserAvatar user={rec.user} />
                  <div>
                    <p className="text-sm text-[#b5b5b5]">
                      Referred on{" "}
                      {new Date(rec.created_at.value).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">
                    Candidate Details
                  </h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-[#b5b5b5]">

                      <span className="text-[#eaeaea]">
                        {rec.recommendation.value.map((person, idx) => (
                          <div key={idx} className="mb-4">
                            <p className="text-[#eaeaea]">
                            Name:{" "} {person.first_name.value} {person.last_name.value}
                            </p>
                          </div>
                        ))}
                      </span>
                    </p>
                  </div>
                </div>

                <div>
                  <h4 className="mb-2 font-medium text-[#eaeaea]">
                    Referred By
                  </h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-[#b5b5b5]">
                      Name:{" "}
                      <span className="text-[#eaeaea]">
                        {rec.candidate.first_name.value}{" "}
                        {rec.candidate.last_name.value}
                      </span>
                    </p>

                    {rec.candidate.online_accounts.value.length > 0 && (
                      <div>
                        <p className="text-[#b5b5b5]">Online Accounts:</p>
                        <ul className="ml-4 list-disc">
                          {rec.candidate.online_accounts.value.map(
                            (account, i) => (
                              <li key={i}>
                                <a
                                  href={account?.url?.value}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-[#7dd87d] hover:underline"
                                >
                                  {account?.url?.value}
                                </a>
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const RepostModal = ({
  isOpen,
  onClose,
  referralId,
  communities,
  referrals,
}) => {
  if (!isOpen) return null;

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [selectedCommunity, setSelectedCommunity] = useState("");
  const [loading, setLoading] = useState(false);

  // Find the current referral from the parent component's referrals state
  const currentReferral = referrals.find((ref) => ref.id.value === referralId);

  // Debounced search function for users
  const debouncedUserSearch = useCallback(
    debounce(async (query) => {
      if (query.trim().length < 2) {
        setSuggestedUsers([]);
        setShowUserSuggestions(false);
        return;
      }

      try {
        setSearchingUsers(true);
        const sdk = new MkdSDK();
        const response = await sdk.SearchUsers(query);

        if (!response.error) {
          setSuggestedUsers(response.list || []);
          setShowUserSuggestions(true);
        }
      } catch (err) {
        console.error("Failed to search users:", err);
      } finally {
        setSearchingUsers(false);
      }
    }, 300),
    []
  );

  // Handle user search input change
  const handleUserSearchChange = (e) => {
    const value = e.target.value;
    setUserSearchQuery(value);
    debouncedUserSearch(value);
  };

  // Handle user selection
  const handleUserSelect = (user) => {
    setSelectedUser(user);
    setUserSearchQuery(user.name.value || 'No name');
    setShowUserSuggestions(false);
    setSuggestedUsers([]);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showUserSuggestions && !event.target.closest('.user-search-container')) {
        setShowUserSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserSuggestions]);

  const handleRepost = async () => {
    if (repostType === "community" && !selectedCommunity) return;
    if (repostType === "user" && !selectedUser) return;

    setLoading(true);

    try {
      const sdk = new MkdSDK();

      // Prepare payload based on repost type
      const payload = {
        referral_id: referralId,
      };

      if (repostType === "community") {
        payload.community_id = selectedCommunity;
      } else {
        payload.user_id = selectedUser.id.value;
      }

      await sdk.RepostReferral(payload);

      showToast(
        globalDispatch,
        `Referral reposted to ${repostType === "community" ? "community" : "user"} successfully!`,
        5000,
        "success"
      );
      onClose(true); // Pass true to trigger refresh
    } catch (err) {
      showToast(
        globalDispatch,
        err.message || "Failed to repost referral",
        5000,
        "error"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center">
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={() => !loading && onClose()}
      />

      <div className="relative z-50 w-full max-w-md rounded-lg bg-[#161616] p-6 shadow-xl">
        {/* Referral Details Section */}
        {currentReferral && (
          <div className="mb-6">
            <div className="flex gap-3 items-center mb-4">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#242424] text-[#eaeaea]">
                {currentReferral.creator.name.value.charAt(0)}
              </div>
              <div>
                <h3 className="text-lg font-medium text-[#eaeaea]">
                  {currentReferral.title.value}
                </h3>
                <p className="text-sm text-[#b5b5b5]">
                  {currentReferral.creator.name.value} -{" "}
                  {formatDate(currentReferral.created_at.value)}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Repost Type Selection */}
        <h3 className="mb-4 text-lg font-medium text-[#eaeaea]">
          Choose where to repost this referral
        </h3>

        <div className="mb-6 flex gap-4">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="repostType"
              value="community"
              checked={repostType === "community"}
              onChange={(e) => setRepostType(e.target.value)}
              className="text-[#2e7d32] focus:ring-[#2e7d32]"
            />
            <span className="text-[#eaeaea]">Repost to Community</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="repostType"
              value="user"
              checked={repostType === "user"}
              onChange={(e) => setRepostType(e.target.value)}
              className="text-[#2e7d32] focus:ring-[#2e7d32]"
            />
            <span className="text-[#eaeaea]">Repost to Person</span>
          </label>
        </div>

        {/* Community Selection */}
        {repostType === "community" && (
          <div className="mb-6">
            <label className="mb-2 block text-sm font-medium text-[#b5b5b5]">
              Select Community
            </label>
            <select
              value={selectedCommunity}
              onChange={(e) => setSelectedCommunity(e.target.value)}
              className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
              required
            >
              <option value="">Select a community</option>
              {communities.map((community) => (
                <option key={community.id.value} value={community.id.value}>
                  {community.title.value}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* User Selection */}
        {repostType === "user" && (
          <div className="mb-6 user-search-container relative">
            <label className="mb-2 block text-sm font-medium text-[#b5b5b5]">
              Search for Person
            </label>
            <input
              type="text"
              value={userSearchQuery}
              onChange={handleUserSearchChange}
              placeholder="Type name or email to search..."
              className="h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
            />

            {/* User suggestions dropdown */}
            {showUserSuggestions && (
              <div className="absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] py-2 shadow-lg max-h-60 overflow-y-auto">
                {searchingUsers ? (
                  <div className="px-4 py-2 text-[#b5b5b5]">Searching...</div>
                ) : suggestedUsers.length > 0 ? (
                  suggestedUsers.map((user) => (
                    <div
                      key={user.id.value}
                      onClick={() => handleUserSelect(user)}
                      className="cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#2e7d32] flex items-center gap-3"
                    >
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white">
                        {user.name.value ? user.name.value.charAt(0) : 'U'}
                      </div>
                      <div>
                        <div className="font-medium">{user.name.value || 'No name'}</div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="px-4 py-2 text-[#b5b5b5]">No users found</div>
                )}
              </div>
            )}

            {selectedUser && (
              <div className="mt-2 flex items-center gap-3 rounded-lg border border-[#363636] bg-[#1e1e1e] p-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white">
                  {selectedUser.name.value ? selectedUser.name.value.charAt(0) : 'U'}
                </div>
                <div className="flex-1">
                  <div className="font-medium text-[#eaeaea]">{selectedUser.name.value || 'No name'}</div>
                </div>
                <button
                  onClick={() => {
                    setSelectedUser(null);
                    setUserSearchQuery("");
                  }}
                  className="text-[#b5b5b5] hover:text-[#eaeaea]"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        )}

        <div className="flex gap-4 justify-end">
          <button
            onClick={() => !loading && onClose()}
            disabled={loading}
            className="rounded-lg border border-[#363636] px-6 py-2 text-sm text-[#eaeaea] hover:bg-[#242424] disabled:opacity-50"
          >
            Cancel
          </button>
          <button
            onClick={handleRepost}
            disabled={loading || (repostType === "community" && !selectedCommunity) || (repostType === "user" && !selectedUser)}
            className="rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50"
          >
            {loading ? "Reposting..." : "Repost"}
          </button>
        </div>
      </div>
    </div>
  );
};

const ReadMore = ({ text = '', maxWords = 100 }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Handle null/undefined/empty text
  if (!text) {
    return <p className="whitespace-pre-line text-[#b5b5b5]">No content available</p>;
  }

  const words = text.split(/\s+/);
  const needsReadMore = words.length > maxWords;

  const displayText = isExpanded ? text : words.slice(0, maxWords).join(' ') + (needsReadMore ? '...' : '');

  return (
    <div>
      <p className="whitespace-pre-line text-[#b5b5b5]">
        {displayText}
        {needsReadMore && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 text-[#7dd87d] hover:underline"
          >
            {isExpanded ? 'Read Less' : 'Read More'}
          </button>
        )}
      </p>
    </div>
  );
};

const ReferralDetailsModal = ({ isOpen, onClose, referral }) => {
  const [activeTab, setActiveTab] = useState("details");
  const [notes, setNotes] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newNoteContent, setNewNoteContent] = useState("");
  const [newTaskData, setNewTaskData] = useState({
    content: "",
    due_date: ""
  });
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  useEffect(() => {
    if (isOpen && referral) {
      loadNotes();
      loadTasks();
    }
  }, [isOpen, referral]);

  const loadNotes = async () => {
    try {
      const sdk = new MkdSDK();
      const notesResponse = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes?referral_id=${referral.id.value}`,
        {},
        "GET"
      );

      if (!notesResponse.error) {
        // Sort notes by created_at in descending order (latest first)
        const sortedNotes = (notesResponse.list || []).sort((a, b) => {
          return new Date(b.created_at) - new Date(a.created_at);
        });
        setNotes(sortedNotes);
      }
    } catch (err) {
      console.error("Failed to load notes:", err);
    }
  };

  const loadTasks = async () => {
    try {
      const sdk = new MkdSDK();
      const tasksResponse = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks?referral_id=${referral.id.value}`,
        {},
        "GET"
      );

      if (!tasksResponse.error) {
        // Sort tasks by due_date in ascending order (closest due date first)
        const sortedTasks = (tasksResponse.list || []).sort((a, b) => {
          return new Date(a.due_date) - new Date(b.due_date);
        });
        setTasks(sortedTasks);
      }
    } catch (err) {
      console.error("Failed to load tasks:", err);
    }
  };

  const handleAddTask = async () => {
    if (!newTaskData.content.trim() || !newTaskData.due_date) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/tasks",
        {
          content: { value: newTaskData.content },
          due_date: { value: newTaskData.due_date },
          referral_id: { value: referral?.id?.value }
        },
        "POST"
      );

      if (!response.error) {
        // Create a new task with current timestamp while waiting for API response
        const newTask = {
          id: response.model?.id || Date.now(),
          description: newTaskData.content,
          due_date: newTaskData.due_date,
          created_at: new Date().toISOString(),
          title: `Task added on ${new Date().toLocaleDateString()}`
        };

        // Add new task to the list
        setTasks(prev => [...prev, newTask]);
        setNewTaskData({ content: "", due_date: "" });
        setIsAddingTask(false);

        // Refresh tasks to get the correct data from server
        loadTasks();

        showToast(globalDispatch, "Task added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add task");
      }
    } catch (err) {
      console.error("Error adding task:", err);
      showToast(globalDispatch, err.message || "Failed to add task", 5000, "error");
    }
  };

  const handleDeleteTask = async (taskId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/tasks/${taskId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted task from the list
        setTasks(prev => prev.filter(task => task.id !== taskId));
        showToast(globalDispatch, "Task deleted successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to delete task");
      }
    } catch (err) {
      console.error("Error deleting task:", err);
      showToast(globalDispatch, err.message || "Failed to delete task", 5000, "error");
    }
  };

  const handleAddNote = async () => {
    if (!newNoteContent.trim()) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/notes",
        {
          content: { value: newNoteContent },
          referral_id: { value: referral?.id?.value }
        },
        "POST"
      );

      if (!response.error) {
        // Create a new note with current timestamp while waiting for API response
        const timestamp = new Date().toISOString();
        const newNote = {
          id: response.model?.id?.value || Date.now(),
          description: newNoteContent,
          created_at: response.model?.created_at?.value || timestamp,
        };

        // Add new note at the beginning of the array
        setNotes(prev => [newNote, ...prev]);
        setNewNoteContent("");
        setIsAddingNote(false);

        // Refresh notes to get the correct data from server
        loadNotes();

        showToast(globalDispatch, "Note added successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to add note");
      }
    } catch (err) {
      console.error("Error adding note:", err);
      showToast(globalDispatch, err.message || "Failed to add note", 5000, "error");
    }
  };

  const handleDeleteNote = async (noteId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/notes/${noteId}`,
        {},
        "DELETE"
      );

      if (!response.error) {
        // Remove the deleted note from the list
        setNotes(prev => prev.filter(note => note.id !== noteId));
        showToast(globalDispatch, "Note deleted successfully!", 5000, "success");
      } else {
        throw new Error(response.message || "Failed to delete note");
      }
    } catch (err) {
      console.error("Error deleting note:", err);
      showToast(globalDispatch, err.message || "Failed to delete note", 5000, "error");
    }
  };

  if (!isOpen || !referral) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-4">
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
          aria-hidden="true"
        />

        <div className="relative z-50 w-full max-w-3xl rounded-lg bg-[#161616] shadow-xl">
          <div className="flex flex-col h-[85vh]">
            <div className="sticky top-0 z-50 flex justify-between items-center bg-[#161616] p-6 border-b border-[#363636]">
              <div className="flex gap-3 items-center">
                <UserAvatar user={referral.creator} />
                <div>
                  <h3 className="text-xl font-semibold text-[#eaeaea]">
                    {referral?.title?.value || 'No Title'}
                  </h3>
                  <p className="text-sm text-[#b5b5b5]">
                    {referral?.creator?.name?.value || 'Unknown'} -{" "}
                    {formatDate(referral?.created_at?.value)}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-[#b5b5b5] hover:text-[#eaeaea] p-2"
              >
                <svg className="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>

            {/* Tabs */}
            <div className="border-b border-[#363636] px-6">
              <div className="flex gap-4">
                <button
                  onClick={() => setActiveTab("details")}
                  className={`border-b-2 py-2 px-1 text-sm ${
                    activeTab === "details"
                      ? "border-[#7dd87d] text-[#7dd87d]"
                      : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                  }`}
                >
                  Details
                </button>
                <button
                  onClick={() => setActiveTab("notes")}
                  className={`border-b-2 py-2 px-1 text-sm ${
                    activeTab === "notes"
                      ? "border-[#7dd87d] text-[#7dd87d]"
                      : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                  }`}
                >
                  Notes
                </button>
                <button
                  onClick={() => setActiveTab("tasks")}
                  className={`border-b-2 py-2 px-1 text-sm ${
                    activeTab === "tasks"
                      ? "border-[#7dd87d] text-[#7dd87d]"
                      : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
                  }`}
                >
                  Tasks
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto p-6">
              <div className={`h-full ${activeTab === "details" ? "block" : "hidden"}`}>
                <div className="space-y-6">
                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Type</h4>
                    <p className="text-[#b5b5b5]">
                      {referral?.type?.value
                        ? referral.type.value === "full_time"
                          ? "Full time"
                          : referral.type.value
                        : 'N/A'}
                    </p>
                  </div>

                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Description</h4>
                    <ReadMore text={referral?.description?.value} maxWords={1000} />
                  </div>

                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Deal Size</h4>
                    <p className="text-[#b5b5b5]">${referral?.deal_size?.value || 'N/A'}</p>
                  </div>

                  {referral?.expiration_date?.value && (
                    <div>
                      <h4 className="mb-2 font-medium text-[#eaeaea]">Expiration Date</h4>
                      <p className="text-[#b5b5b5]">{formatDate(referral.expiration_date.value)}</p>
                    </div>
                  )}

                  {referral?.description_image?.value && (
                    <div>
                      <h4 className="mb-2 font-medium text-[#eaeaea]">Attached Image</h4>
                      <img
                        src={referral.description_image.value}
                        alt="Description"
                        className="max-h-96 rounded-lg object-contain"
                      />
                    </div>
                  )}

                  <div>
                    <h4 className="mb-2 font-medium text-[#eaeaea]">Status</h4>
                    <span className="rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]">
                      {referral?.status?.value || 'Unknown'}
                    </span>
                  </div>
                </div>
              </div>

              <div className={`h-full ${activeTab === "notes" ? "block" : "hidden"}`}>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="text-lg font-medium text-[#eaeaea]">Notes</h4>
                    <button
                      onClick={() => setIsAddingNote(true)}
                      className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                    >
                      <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Add Note
                    </button>
                  </div>

                  {isAddingNote && (
                    <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
                      <textarea
                        value={newNoteContent}
                        onChange={(e) => setNewNoteContent(e.target.value)}
                        placeholder="Write your note..."
                        className="mb-4 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-[#eaeaea]"
                        rows={3}
                      />
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => setIsAddingNote(false)}
                          className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleAddNote}
                          className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                        >
                          Add Note
                        </button>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    {notes.map((note) => (
                      <div
                        key={note.id}
                        className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4"
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="text-[#eaeaea]">{note.description}</p>
                            <p className="mt-2 text-sm text-[#b5b5b5]">
                              {formatDate(note.created_at)}
                            </p>
                          </div>
                          <button
                            onClick={() => handleDeleteNote(note.id)}
                            className="text-[#b5b5b5] hover:text-[#dc3545]"
                          >
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className={`h-full ${activeTab === "tasks" ? "block" : "hidden"}`}>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="text-lg font-medium text-[#eaeaea]">Tasks</h4>
                    <button
                      onClick={() => setIsAddingTask(true)}
                      className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                    >
                      <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                        <path
                          fillRule="evenodd"
                          d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Add Task
                    </button>
                  </div>

                  {isAddingTask && (
                    <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
                      <div className="mb-4">
                        <label className="mb-2 block text-sm text-[#b5b5b5]">
                          Task Title
                        </label>
                        <input
                          type="text"
                          value={newTaskData.content}
                          onChange={(e) => setNewTaskData({...newTaskData, content: e.target.value})}
                          placeholder="Enter task title..."
                          className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                        />
                      </div>
                      <div className="mb-4">
                        <label className="mb-2 block text-sm text-[#b5b5b5]">
                          Due Date
                        </label>
                        <input
                          type="date"
                          value={newTaskData.due_date}
                          onChange={(e) => setNewTaskData({...newTaskData, due_date: e.target.value})}
                          className="h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => setIsAddingTask(false)}
                          className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleAddTask}
                          className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
                        >
                          Add Task
                        </button>
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    {tasks.length === 0 ? (
                      <p className="text-center text-[#b5b5b5] py-4">No tasks yet. Create your first task!</p>
                    ) : (
                      tasks.map((task) => (
                        <div
                          key={task.id}
                          className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4"
                        >
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="text-[#eaeaea] font-medium">{task.description}</p>
                              <p className="mt-1 text-sm text-[#b5b5b5]">
                                Due: {formatDate(task.due_date)}
                              </p>
                            </div>
                            <button
                              onClick={() => handleDeleteTask(task.id)}
                              className="text-[#b5b5b5] hover:text-[#dc3545]"
                            >
                              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                />
                              </svg>
                            </button>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const ReferralsPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [error, setError] = useState("");
  const { checkForNewReferrals, initializeDataCount } = useNotificationTrigger();
  const [loading, setLoading] = useState(true);

  // Get active tab from URL params, default to 'referrals-feed'
  const activeTab = searchParams.get('tab') || location.state?.activeTab || "referrals-feed";

  // Tab change handler
  const handleTabChange = (tab) => {
    setSearchParams({ tab });
  };
  const [referrals, setReferrals] = useState([]);
  const [allReferrals, setAllReferrals] = useState([]); // Store all unfiltered referrals
  const [searchQuery, setSearchQuery] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [editingReferral, setEditingReferral] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [referralToDelete, setReferralToDelete] = useState(null);
  const [showRecommendForm, setShowRecommendForm] = useState(false);
  const [selectedReferralId, setSelectedReferralId] = useState(null);
  const [showRecommendations, setShowRecommendations] = useState(false);
  const [selectedRecommendations, setSelectedRecommendations] = useState([]);
  const [activityData, setActivityData] = useState([]); // New state for activity data

  // Add these states to ReferralsPage
  const [showRepostModal, setShowRepostModal] = useState(false);
  const [referralToRepost, setReferralToRepost] = useState(null);
  const [joinedCommunities, setJoinedCommunities] = useState([]);





  // Add handleViewDetails function
  const handleViewDetails = (referral) => {
    navigate(`/member/referrals/${referral.id.value}/details`);
  };

  // Function to set payment method as default
  const setPaymentMethodAsDefault = async () => {
    try {
      const sdk = new MkdSDK();
      await sdk.callRawAPI(
        "/v1/api/dealmaker/user/payment-methods/account/set-default",
        {},
        "POST"
      );
      console.log("Payment method set as default");
    } catch (error) {
      console.error("Failed to set payment method as default:", error);
    }
  };

  // Function to check and set payment method as default if needed
  const checkAndSetDefaultPaymentMethod = async () => {
    try {
      const sdk = new MkdSDK();
      const methodsData = await sdk.GetPaymentMethods();

      if (methodsData.list && methodsData.list.length > 0) {
        const nonDefaultMethod = methodsData.list.find(method => method.is_default.value === 0);
        if (nonDefaultMethod) {
          await setPaymentMethodAsDefault();
        }
      }
    } catch (error) {
      console.error("Failed to check payment methods:", error);
    }
  };



  // Add this useEffect to load joined communities and check payment methods
  useEffect(() => {
    const loadJoinedCommunities = async () => {
      try {
        const sdk = new MkdSDK();
        const response = await sdk.GetJoinedCommunities();
        if (!response.error) {
          setJoinedCommunities(response.list || []);
        }
      } catch (err) {
        console.error("Failed to load communities:", err);
      }
    };

    const initializeData = async () => {
      await loadJoinedCommunities();

      // Check and set payment method as default if needed (optional)
      await checkAndSetDefaultPaymentMethod();
    };

    initializeData();
  }, []);

  // Effect to load referrals when tab changes
  useEffect(() => {
    // Clear search when changing tabs
    if (searchQuery.trim()) {
      setSearchQuery("");
    }
    loadReferrals();
  }, [activeTab]);

  // Note: Global polling for new referrals is now handled by NotificationContext
  // This ensures notifications appear on all pages, not just when on ReferralsPage

  // Add debug function to window for testing
  useEffect(() => {
    window.debugNotifications = () => {
      console.log('=== Notification Debug Info ===');
      console.log('Active tab:', activeTab);
      console.log('Referrals count:', referrals.length);
      console.log('Last referral check:', localStorage.getItem('referrals_last_check'));
      console.log('Last chat check:', localStorage.getItem('chats_last_check'));
      console.log('Recent referrals:', referrals.slice(0, 3).map(r => ({
        id: r.id?.value,
        title: r.title?.value,
        created_at: r.created_at?.value
      })));
    };

    window.forceCheckReferralsPage = () => {
      console.log('Force checking referrals on page...');
      loadReferrals();
    };
  }, [activeTab, referrals]);

  // Effect to filter referrals when search query changes
  useEffect(() => {
    if (allReferrals.length > 0) {
      filterReferrals();
    }
  }, [searchQuery, allReferrals]);

  // Function to filter referrals based on search query
  const filterReferrals = () => {
    if (!searchQuery.trim() || !allReferrals.length) {
      // If no search query or no referrals, show all referrals
      setReferrals(allReferrals);
      return;
    }

    // Filter referrals by title, description, and requirements (case-insensitive)
    const query = searchQuery.toLowerCase();

    // First, filter by search terms
    const searchFiltered = allReferrals.filter(referral => {
      // Check if title contains the search query
      const titleMatch = referral.title.value.toLowerCase().includes(query);

      // Check description if available
      const descriptionMatch = referral.description?.value
        ? referral.description.value.toLowerCase().includes(query)
        : false;

      // Check requirements if available
      const requirementsMatch = referral.requirements?.value
        ? referral.requirements.value.toLowerCase().includes(query)
        : false;

      // Add search match flags to the referral object
      if (titleMatch || descriptionMatch || requirementsMatch) {
        referral._searchMatches = {
          title: titleMatch,
          description: descriptionMatch,
          requirements: requirementsMatch
        };
      }

      // Return true if any field matches
      return titleMatch || descriptionMatch || requirementsMatch;
    });

    console.log(`Filtered ${allReferrals.length} referrals to ${searchFiltered.length} results for query "${query}"`);
    setReferrals(searchFiltered);
  };

  // Handle search input changes
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);

    // If search is cleared, immediately show all referrals
    if (!value.trim() && allReferrals.length > 0) {
      setReferrals(allReferrals);
    }
  };

  const loadReferrals = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();

      // Use the regular API endpoints to load referrals
      const [referralsResponse, activityResponse] = await Promise.all([
        activeTab === "referrals-feed"
          ? sdk.GetReferralsFeed()
          : activeTab === "my-referrals"
          ? sdk.GetMyReferrals()
          : sdk.GetArchivedReferrals(),
        sdk.callRawAPI("/v1/api/dealmaker/user/earnings/activity", {}, "GET"),
      ]);

      if (!referralsResponse.error) {
        // Create a map of referral payment status from activity data
        const paymentStatusMap = {};
        if (!activityResponse.error && activityResponse.list) {
          activityResponse.list.forEach((activity) => {
            const referralId = activity.referral_id?.value;
            if (referralId) {
              paymentStatusMap[referralId] = activity.status?.value;
            }
          });
        }

        // Merge payment status into referrals data
        const referralsWithPaymentStatus = referralsResponse.list.map(
          (referral) => ({
            ...referral,
            payment_status: {
              value:
                paymentStatusMap[referral.id.value] ||
                referral.payment_status?.value ||
                "pending",
            },
          })
        );

        // Filter based on tab and referral_type
        let filteredReferrals = referralsWithPaymentStatus;

        if (activeTab === "my-referrals") {
          // For "My Referrals" tab, exclude reposted referrals
          filteredReferrals = referralsWithPaymentStatus.filter(
            (referral) => referral.referral_type?.value !== "reposted"
          );
        }

        // Store all referrals for filtering, regardless of tab
        setAllReferrals(filteredReferrals);

        // Check for new referrals and trigger notifications
        if (activeTab === "referrals-feed") {
          console.log(`[Notifications] Checking referrals feed: ${filteredReferrals.length} items`);
          checkForNewReferrals(filteredReferrals);
        }

        // If there's a search query, filter the referrals
        if (searchQuery.trim()) {
          filterReferrals();
        } else {
          // Otherwise, show filtered referrals based on tab
          setReferrals(filteredReferrals);
        }

        setActivityData(activityResponse.list || []);
      } else {
        setError(referralsResponse.message);
      }
    } catch (err) {
      console.error("Failed to load referrals:", err);
      setError(err.message || "Failed to load referrals");
    } finally {
      setLoading(false);
    }
  };

  const handleMarkComplete = async (referralId) => {
    try {
      const sdk = new MkdSDK();
      const referral = referrals.find((ref) => ref.id.value === referralId);

      // Get the user_id of the person who got the commission
      const userId = referral.recommendations.value[0]?.user?.id.value;
      console.log("referral",referral)
      console.log("userid",userId)
      // const response = {}
      if(!userId){
        showToast(
          globalDispatch,
          "No recommendation found",
          5000,
          "error"
        );
      }
      if(userId){
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/referral/${referralId}/complete`,
        {
          user_id: {value:userId}
        },
        "POST"
      );

      if (!response.error) {
        setReferrals((prevReferrals) =>
          prevReferrals.map((ref) =>
            ref.id.value === referralId
              ? {
                  ...ref,
                  status: { value: "completed" },
                  payment_status: { value: "pending" },
                }
              : ref
          )
        );
        showToast(
          globalDispatch,
          "Referral marked as complete! Please proceed with payment.",
          5000,
          "success"
        );

        // Since this is called by the referral owner, navigate to PaymentStatusPage
        navigate(`/member/referrals/${referral.id.value}/payment`, {
          state: { referral: referral },
        });
      } else {
        setError(response.message);
        showToast(globalDispatch, response.message, 5000, "error");
      }
    }
    } catch (err) {
      setError(err.message || "Failed to update referral");
      showToast(
        globalDispatch,
        err.message || "Failed to update referral",
        5000,
        "error"
      );
    }
  };

  const handleDelete = async (referralId) => {
    setReferralToDelete(referralId);
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.UpdateReferralStatus(
        referralToDelete,
        "deleted"
      );

      if (!response.error) {
        setReferrals((prevReferrals) =>
          prevReferrals.filter((ref) => ref.id.value !== referralToDelete)
        );
        showToast(
          globalDispatch,
          "Referral deleted successfully!",
          5000,
          "success"
        );
      }
    } catch (err) {
      setError(err.message || "Failed to update referral");
    } finally {
      setDeleteModalOpen(false);
      setReferralToDelete(null);
    }
  };

  const handleArchive = async (referralId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.UpdateReferralStatus(referralId, "archived");

      if (!response.error) {
        setReferrals(prevReferrals =>
          prevReferrals.filter(ref => ref.id.value !== referralId)
        );
        showToast(globalDispatch, "Referral archived successfully!", 5000, "success");
      }
    } catch (err) {
      setError(err.message || "Failed to update referral");
    }
  };

  const handleUnArchive = async (referralId) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.UpdateReferralStatus(referralId, "completed");

      if (!response.error) {
        setReferrals(prevReferrals =>
          prevReferrals.filter(ref => ref.id.value !== referralId)
        );
        showToast(globalDispatch, "Referral unarchived successfully!", 5000, "success");
      }
    } catch (err) {
      setError(err.message || "Failed to update referral");
    }
  };

  const handleRefer = (referralId) => {
    setSelectedReferralId(referralId);
    setShowRecommendForm(true);
  };

  const handleRepost = (referralId) => {
    setReferralToRepost(referralId);
    setShowRepostModal(true);
  };

  const renderHeader = () =>
    activeTab === "my-referrals" ? (
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-[#eaeaea]">My Opportunities</h1>
          <p className="text-[#b5b5b5]">
            View Referrals - Sent and Received
          </p>
        </div>
        <div className="flex gap-4 items-center">
          <div className="flex relative items-center">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder="Search your Opportunity"
              className="h-10 w-64 rounded-lg border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea]"
            />
            {searchQuery ? (
              <button
                onClick={() => {
                  setSearchQuery("");
                  setReferrals(allReferrals);
                }}
                className="absolute right-3 cursor-pointer"
                title="Clear search"
              >
                <svg
                  className="h-4 w-4 text-[#b5b5b5] hover:text-[#eaeaea]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            ) : (
              <div className="absolute right-3">
                <svg
                  className="h-4 w-4 text-[#b5b5b5]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            )}
          </div>
          <Link to="/member/referrals/add">
            {" "}
            <button className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]">
              <span>+</span> Post a New Opportunity
            </button>
          </Link>
        </div>
      </div>
    ) : activeTab === "referrals-feed" ? (
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-[#eaeaea]">Opportunities Feed</h1>
          <p className="text-[#b5b5b5]">
            Browse and discover opportunities from the community.
          </p>
        </div>
        <div className="flex gap-4 items-center">
          <div className="flex relative items-center">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder="Search opportunities..."
              className="h-10 w-64 rounded-lg border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea]"
            />
            {searchQuery ? (
              <button
                onClick={() => {
                  setSearchQuery("");
                  setReferrals(allReferrals);
                }}
                className="absolute right-3 cursor-pointer"
                title="Clear search"
              >
                <svg
                  className="h-4 w-4 text-[#b5b5b5] hover:text-[#eaeaea]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            ) : (
              <div className="absolute right-3">
                <svg
                  className="h-4 w-4 text-[#b5b5b5]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            )}
          </div>
          <Link to="/member/referrals/add">
            <button className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]">
              <span>+</span> Post an Opportunity
            </button>
          </Link>
        </div>
      </div>

    ) : activeTab === "archived" ? (
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-[#eaeaea]">
            Archived Opportunities
          </h1>
          <p className="text-[#b5b5b5]">
            Archive or unarchive your Open or Direct Opportunities
          </p>
        </div>
        <div className="flex gap-4 items-center">
          <div className="flex relative items-center">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder="Search archived opportunities..."
              className="h-10 w-64 rounded-lg border border-[#363636] bg-[#161616] pl-4 pr-10 text-[#eaeaea] placeholder-[#b5b5b5]"
            />
            {searchQuery ? (
              <button
                onClick={() => {
                  setSearchQuery("");
                  setReferrals(allReferrals);
                }}
                className="absolute right-3 cursor-pointer"
                title="Clear search"
              >
                <svg
                  className="h-4 w-4 text-[#b5b5b5] hover:text-[#eaeaea]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            ) : (
              <div className="absolute right-3">
                <svg
                  className="h-4 w-4 text-[#b5b5b5]"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>
    ) : null;

  const renderReferralType = (referral) => {

    // Safety check - if referral_type is missing, default to "open referral"
    if (!referral.referral_type || !referral.referral_type.value) {
      return (
        <div className="flex gap-2 items-center">
          <OpenReferralIcon />
          <span className="text-sm text-[#eaeaea]">Referral</span>
        </div>
      );
    }

    if (referral.referral_type.value === "reposted") {
      return (
        <div className="flex gap-2 items-center">
          <RepostedIcon />
          <span className="text-sm text-[#eaeaea]">
            Reposted by {referral.reposted_by?.name?.value || "Unknown"}
          </span>
        </div>
      );
    }

    if (referral.referral_type.value === "open referral") {
      return (
        <div className="flex gap-2 items-center">
          <OpenReferralIcon />
          <span className="text-sm text-[#eaeaea]">Open Referral</span>
        </div>
      );
    }

    if (referral.referral_type.value === "community referral") {
      return (
        <div className="flex gap-2 items-center">
          <OpenReferralIcon />
          <span className="text-sm text-[#eaeaea]">Community Referral</span>
        </div>
      );
    }

    if (referral.referral_type.value === "direct referral") {
      // Safety check for referred_to
      const referredToName = referral.referred_to?.name?.value || "Someone";

      return (
        <div className="flex gap-2 items-center">
          <FlashIcon />
          <span className="text-sm text-[#eaeaea]">
            Direct Referral To {referredToName}
          </span>
        </div>
      );
    }

    // Default case for any other type
    return (
      <div className="flex gap-2 items-center">
        <OpenReferralIcon />
        <span className="text-sm text-[#eaeaea] capitalize">{referral.referral_type.value}</span>
      </div>
    );
  };

  const renderReferralActions = (referral) => {
    // Safety check - if referral doesn't have required properties, show minimal actions
    if (!referral || !referral.id || !referral.status) {
      return (
        <div className="flex gap-3 items-center">
          <button
            onClick={() => handleViewDetails(referral)}
            className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
            View Details
          </button>
        </div>
      );
    }

    // Find matching activity for this referral
    const referralActivity = activityData.find(
      (activity) => activity?.id?.value === referral.id.value
    );
    const isPaid = referralActivity?.status?.value === "completed";

    return (
      <div className="flex gap-3 items-center">
        {referral.status.value === "completed" ? (
          <>
            <button
              onClick={() => handleArchive(referral.id.value)}
              className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 7l-8 8-4-4"
                />
              </svg>
              Archive
            </button>

            {referral.owner.value ? (
              // Show payment status button for referral owner - goes to PaymentStatusPage
              <button
                onClick={() => {
                  navigate(`/member/referrals/${referral.id.value}/payment`, {
                    state: { referral: referral },
                  });
                }}
                className={`flex items-center gap-2 rounded-lg px-4 py-2 text-sm ${
                  isPaid
                    ? "border border-[#2e7d32] bg-[#2e7d3233] text-[#7dd87d]"
                    : "bg-[#1976d2] text-white"
                }`}
                disabled={isPaid}
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  />
                </svg>
                {isPaid ? "Payment Complete" : "Make Payment"}
              </button>
            ) : (
              // Show payment status for referred users - goes to ReferralPaymentPage
              <button
                onClick={() =>
                  navigate(
                    `/member/referrals/${referral.id.value}/payment-status`
                  )
                }
                className="flex items-center gap-2 rounded-lg border border-[#2e7d32] bg-[#2e7d3233] px-4 py-2 text-sm text-[#7dd87d]"
              >
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                  />
                </svg>
                View Commission Status
              </button>
            )}

            <button
              onClick={() => handleDelete(referral.id.value)}
              className="flex items-center gap-2 rounded-lg bg-[#dc3545] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
              Delete
            </button>
          </>
        ) : referral.status.value === "archived" ? (
          // ... rest of the existing code ...
          <>
            <button
              onClick={() => handleUnArchive(referral.id.value)}
              className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#2e7d3233] px-4 py-2 text-sm text-[#7dd87d]"
            >
              Unarchive
            </button>
          </>
        ) : referral.owner.value ? (
          // Show these buttons if user owns the referral and it's not completed
          <>
            <button
              onClick={() =>
                navigate(`/member/referrals/edit/${referral.id.value}`)
              }
              className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit
            </button>

            <button
              onClick={() => handleDelete(referral.id.value)}
              className="flex items-center gap-2 rounded-lg bg-[#dc3545] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                />
              </svg>
              Delete
            </button>

            <button
              onClick={() => handleMarkComplete(referral.id.value)}
              className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
              Mark Complete
            </button>
          </>
        ) : (
          // Show these buttons if user doesn't own the referral and it's not completed
          <>
            <button
              onClick={() => handleRefer(referral.id.value)}
              className="flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                />
              </svg>
              Refer
            </button>

            <button
              onClick={() => navigate(`/member/chat/${referral.id.value}`)}
              className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                />
              </svg>
              Chat
            </button>

            <button
              onClick={() => handleRepost(referral.id.value)}
              className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Repost
            </button>
          </>
        )}
      </div>
    );
  };



  return (
    <div className="min-h-screen bg-[#1e1e1e] p-4">
      {error && (
        <Toast
          message={error}
          type={error.includes("success") ? "success" : "error"}
        />
      )}

      {renderHeader()}

      {/* Tabs */}
      <div
        style={
          activeTab === "referrals-feed" && showRecommendForm
            ? {
                margin: "auto",
                width: "700px",
                paddingBottom: "20px",
              }
            : {}
        }
        className="mb-6 border-b border-[#363636]"
      >
        <button
          onClick={() => handleTabChange("referrals-feed")}
          className={`mr-6 border-b-2 pb-2 text-sm ${
            activeTab === "referrals-feed"
              ? "border-[#7dd87d] text-[#7dd87d]"
              : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
          }`}
        >
          Opportunities Feed
        </button>
        <button
          onClick={() => handleTabChange("my-referrals")}
          className={`mr-6 border-b-2 pb-2 text-sm ${
            activeTab === "my-referrals"
              ? "border-[#7dd87d] text-[#7dd87d]"
              : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
          }`}
        >
          My Opportunities
        </button>

        <button
          onClick={() => handleTabChange("recommendations")}
          className={`mr-6 border-b-2 pb-2 text-sm ${
            activeTab === "recommendations"
              ? "border-[#7dd87d] text-[#7dd87d]"
              : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
          }`}
        >
          Referrals
        </button>
        <button
          onClick={() => handleTabChange("archived")}
          className={`mr-6 border-b-2 pb-2 text-sm ${
            activeTab === "archived"
              ? "border-[#7dd87d] text-[#7dd87d]"
              : "border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"
          }`}
        >
          Archived
        </button>

      </div>

      {/* Search Results Summary */}
      {searchQuery.trim() && referrals.length > 0 && (
        <div className="mb-4 p-2 bg-[#1e1e1e] border border-[#363636] rounded-lg">
          <p className="text-[#eaeaea]">
            Found <span className="font-semibold text-[#7dd87d]">{referrals.length}</span>
            {activeTab === "archived" ? " archived" : ""} opportunities matching "{searchQuery}"
          </p>
          <p className="text-[#b5b5b5] text-sm mt-1">
            Searching in title, description, and requirements fields
          </p>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === "recommendations" ? (
        <RecommendationsPage />
      ) : (
        <>
          {/* Referrals List */}
          <div className="space-y-4">
            {activeTab === "referrals-feed" && showRecommendForm && (
              <ReferralRecommendForm
                referralId={selectedReferralId}
                onClose={() => {
                  setShowRecommendForm(false);
                  setSelectedReferralId(null);
                }}
              />
            )}

        {loading ? (
          [...Array(2)].map((_, i) => (
            <SkeletonLoader key={i} className="h-48 w-full rounded-lg" />
          ))
        ) : activeTab === "referrals-feed" && showRecommendForm ? (<></>) :
        referrals.length === 0 && searchQuery.trim().length > 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <svg className="w-16 h-16 mb-4 text-[#363636]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <h3 className="text-xl font-medium text-[#eaeaea] mb-2">No results found</h3>
            <p className="text-[#b5b5b5]">
              No {activeTab === "archived" ? "archived " : ""}opportunities matching "{searchQuery}" were found in titles, descriptions, or requirements. Try a different search term.
            </p>
          </div>
        ) : referrals.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-8 text-center">
            <h3 className="text-xl font-medium text-[#eaeaea] mb-2">No opportunities available</h3>
            <p className="text-[#b5b5b5]">
              {activeTab === "my-referrals"
                ? "You haven't created any opportunities yet."
                : activeTab === "archived"
                  ? "You don't have any archived opportunities."
                  : "There are no opportunities in your feed."}
            </p>
          </div>
        ) : (
          referrals.map((referral) => (
            <div
              key={referral.id.value}
              className="flex flex-col gap-2 rounded-lg border border-[#363636] bg-[#1e1e1e] p-4"
            >
              <div className="flex justify-between items-start">
                <div className="flex gap-3">
                  <UserAvatar user={referral.creator} />
                  <div>
                    <h3 className="text-lg font-semibold text-[#eaeaea] flex items-center">
                      {referral.title.value}
                      {searchQuery.trim() && referral._searchMatches?.title && (
                        <span className="ml-2 text-xs text-[#7dd87d] bg-[#2e7d3233] px-2 py-1 rounded-full">
                          Title match
                        </span>
                      )}
                    </h3>
                    <div className="flex gap-2">
                      <p className="text-sm text-[#b5b5b5]">
                        {referral.creator.name.value} -{" "}
                        {formatDate(referral.created_at.value)}
                      </p>
                      {(activeTab === "my-referrals" ||
                        activeTab === "referrals-feed") &&
                        renderReferralType(referral)}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  {activeTab === "my-referrals" && (
                    <span className="rounded-full flex justify-center items-center bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]">
                      Status: {referral.status.value}
                    </span>
                  )}
                  <button
                    onClick={() => handleViewDetails(referral)}
                    className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    View Details
                  </button>
                </div>
              </div>

              <div className="mb-4">
                <h4 className="mb-2 font-medium text-[#eaeaea]">
                  Description
                  {searchQuery.trim() && referral._searchMatches?.description && (
                    <span className="ml-2 text-xs text-[#7dd87d] bg-[#2e7d3233] px-2 py-1 rounded-full">
                      Match found
                    </span>
                  )}
                </h4>
                <ReadMore text={referral.description.value} />
              </div>

              <div className="flex gap-4 items-center">
                {referral.recommendations?.value?.length > 0 && (
                  <button
                    onClick={() => {
                      setSelectedRecommendations(
                        referral.recommendations.value
                      );
                      setShowRecommendations(true);
                    }}
                    className="flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    View Referrals (
                    {referral.recommendations.value.length})
                  </button>
                )}
              </div>
              {renderReferralActions(referral)}
            </div>
          ))
        )}
      </div>
        </>
      )}

      <ReferralFormModal
        isOpen={showModal || editingReferral !== null}
        onClose={(refresh) => {
          setShowModal(false);
          setEditingReferral(null);
          if (refresh) loadReferrals();
        }}
        referral={editingReferral}
      />

      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setReferralToDelete(null);
        }}
        onConfirm={confirmDelete}
        title="Delete Referral"
        message="Are you sure you want to delete this referral? This action cannot be undone."
      />

      <RecommendationModal
        isOpen={showRecommendations}
        onClose={() => setShowRecommendations(false)}
        recommendations={selectedRecommendations}
      />
      <RepostModal
        isOpen={showRepostModal}
        onClose={(refresh) => {
          setShowRepostModal(false);
          setReferralToRepost(null);
          if (refresh) loadReferrals();
        }}
        referralId={referralToRepost}
        communities={joinedCommunities}
        referrals={referrals}
      />


    </div>
  );
};

export default ReferralsPage;
