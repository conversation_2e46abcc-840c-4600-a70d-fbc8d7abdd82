import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { SkeletonLoader } from "Components/Skeleton";

const ViewUserProfile = () => {
  const { user_id } = useParams();
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [industries, setIndustries] = useState([]);
  const { dispatch } = React.useContext(AuthContext);

  const fetchIndustries = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

      if (!response.error && response.data) {
        setIndustries(response.data);
      } else {
        console.error('Error fetching industries:', response.message);
      }
    } catch (err) {
      console.error('Error in fetchIndustries:', err);
    }
  };

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setLoading(true);
        const sdk = new MkdSDK();
        const result = await sdk.callRawAPI('/v1/api/dealmaker/user/details/' + user_id, {}, 'GET');

        if (!result.error) {
          setProfile(result.model);
        }
      } catch (error) {
        console.error(error.message);
        tokenExpireError(dispatch, error.message);
      } finally {
        setLoading(false);
      }
    };

    if (user_id) {
      fetchUserProfile();
      fetchIndustries();
    }
  }, [user_id, dispatch]);

  const getDisplayValue = (field, isName = false) => {
    if (!field || !field.value) return isName ? '' : 'Not provided';
    return field.value;
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-[#1e1e1e]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#2e7d32]"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-4 md:p-6">
      <div className="mb-6">
        <h1 className="text-xl font-semibold text-[#eaeaea]">User Profile</h1>
        <p className="text-sm text-[#b5b5b5]">View user information</p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-[5fr_1fr]">
        {/* Main Profile Info */}
        <div className="space-y-6">
          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <div className="mb-6 flex items-center gap-4">
              <div className="h-20 w-20 rounded-full bg-[#2e7d32] flex items-center justify-center">
                {profile?.photo?.value ? (
                  <img
                    src={profile.photo.value}
                    alt="Profile"
                    className="h-full w-full rounded-full object-cover"
                  />
                ) : (
                  <div className="text-2xl font-bold text-white">
                    {profile?.first_name?.value?.charAt(0)}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-[#eaeaea]">
                  {getDisplayValue(profile?.first_name, true)} {getDisplayValue(profile?.last_name, true)}
                </h2>
                <p className="text-[#b5b5b5]">ID: {getDisplayValue(profile?.id)}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-2 text-sm font-medium text-[#b5b5b5]">Contact Information</h3>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-[#b5b5b5]">Email</p>
                    <p className="text-[#eaeaea]">{getDisplayValue(profile?.email)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Professional Information</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <p className="text-sm text-[#b5b5b5]">Industry</p>
                <p className="text-[#eaeaea]">{(() => {
                  const industryId = profile?.industry_id?.value;
                  if (!industryId) return 'Not specified';

                  const parsedId = typeof industryId === 'string' ? parseInt(industryId, 10) : industryId;
                  const industry = industries.find(ind => ind.id === parsedId);
                  return industry ? industry.name : 'Not specified';
                })()}</p>
              </div>
              <div>
                <p className="text-sm text-[#b5b5b5]">Referral Fee (%)</p>
                <p className="text-[#eaeaea]">{profile?.referral_fee?.value ? `${profile.referral_fee.value}%` : 'Not specified'}</p>
              </div>
              <div>
                <p className="text-sm text-[#b5b5b5]">LinkedIn Profile</p>
                {profile?.linkedin_url?.value ? (
                  <a
                    href={profile.linkedin_url.value}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#2e7d32] hover:text-[#1b5e20] underline break-all"
                  >
                    {profile.linkedin_url.value}
                  </a>
                ) : (
                  <p className="text-[#eaeaea]">Not provided</p>
                )}
              </div>
              <div>
                <p className="text-sm text-[#b5b5b5]">Payout Method</p>
                <p className="text-[#eaeaea] capitalize">{getDisplayValue(profile?.payout_method)}</p>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-[#363636] bg-[#252525] p-6">
            <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Interests</h3>
            <div>
              <p className="text-sm text-[#b5b5b5] mb-2">Industries I'm Interested In</p>
              {profile?.interested_industries?.value &&
               Array.isArray(profile.interested_industries.value) &&
               profile.interested_industries.value.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {profile.interested_industries.value.map(industryId => {
                    // Parse the ID to an integer if it's a string
                    const parsedId = typeof industryId === 'string' ? parseInt(industryId, 10) : industryId;
                    const industry = industries.find(ind => ind.id === parsedId);
                    return industry ? (
                      <span
                        key={industry.id}
                        className="inline-block rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]"
                      >
                        {industry.name}
                      </span>
                    ) : null;
                  })}
                </div>
              ) : (
                <p className="text-[#eaeaea]">No industries specified</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewUserProfile; 