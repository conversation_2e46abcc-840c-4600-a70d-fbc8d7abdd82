import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { SkeletonLoader } from "Components/Skeleton";
import { useContext } from "react";
import { GlobalContext, showToast } from "Context/Global";
import { useNotificationTrigger } from "../../../hooks/useNotificationTrigger";

const SendIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_4_4231)">
      <path d="M15.5656 0.175119C15.8812 0.393869 16.0468 0.771994 15.9875 1.15012L13.9875 14.1501C13.9406 14.4532 13.7562 14.7189 13.4875 14.8689C13.2187 15.0189 12.8968 15.0376 12.6125 14.9189L8.87496 13.3657L6.73433 15.6814C6.45621 15.9845 6.01871 16.0845 5.63433 15.9345C5.24996 15.7845 4.99996 15.4126 4.99996 15.0001V12.3876C4.99996 12.2626 5.04683 12.1439 5.13121 12.0532L10.3687 6.33762C10.55 6.14074 10.5437 5.83762 10.3562 5.65012C10.1687 5.46262 9.86558 5.45012 9.66871 5.62824L3.31246 11.2751L0.553084 9.89387C0.221834 9.72824 0.00933428 9.39699 -4.07251e-05 9.02824C-0.00941573 8.65949 0.184334 8.31574 0.503084 8.13137L14.5031 0.131369C14.8375 -0.0592555 15.25 -0.0405055 15.5656 0.175119Z" fill="currentColor"/>
    </g>
    <defs>
      <clipPath id="clip0_4_4231">
        <path d="M0 0H16V16H0V0Z" fill="white"/>
      </clipPath>
    </defs>
  </svg>
);

const ChatPage = () => {
  const { id:referral_id } = useParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { checkForNewChats } = useNotificationTrigger();
  const [chats, setChats] = useState([]);
  const [messages, setMessages] = useState([]);
  const [chatDetails, setChatDetails] = useState(null);
  const [id, setId] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [error, setError] = useState("");
  // const referral_id = useParams();
  const [chatStatus, setChatStatus] = useState({
    online: { value: false },
    typing: { value: false },
    last_seen: { value: null }
  });
  const messageEndRef = useRef(null);
  const chatSubscription = useRef(null);
  const heartbeatConnection = useRef(null);
  const typingIndicator = useRef(null);

  // Fetch chats on component mount
  useEffect(() => {
    const initializeChat = async () => {
      try {
        if (referral_id) {
          const sdk = new MkdSDK();
          const response = await sdk.StartNewChat(referral_id, 'referral');
          if (!response.error) {
            setId(response.chat.id.value);
            // Update chat details immediately after starting chat
            setChatDetails(response.chat);
            if (response.chat?.partner?.id?.value) {
              // Initialize chat status with default values
              setChatStatus({
                online: { value: false },
                typing: { value: false },
                last_seen: { value: null }
              });

              // Subscribe to chat status updates
              subscribeToChat();
            }
          }
        }
        await loadChats();
        startHeartbeat();
        setOnlineStatus(true);
      } catch (err) {
        console.error("Failed to initialize chat:", err);
        showToast(globalDispatch, "Failed to initialize chat", 5000, "error");
      }
    };

    initializeChat();

    return () => {
      setOnlineStatus(false);
      if (heartbeatConnection.current) {
        heartbeatConnection.current.close();
      }
      if (chatSubscription.current) {
        chatSubscription.current.close();
      }
    };
  }, [referral_id]);

  // Fetch messages when chat ID changes
  useEffect(() => {
    if (id) {
      loadChatMessages();
      subscribeToChat();
    }
  }, [id]);

  // Auto-scroll to bottom on new messages
  useEffect(() => {
    messageEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);



  // Create a new typing indicator when chat ID changes
  useEffect(() => {
    if (id) {
      const sdk = new MkdSDK();
      typingIndicator.current = sdk.CreateTypingIndicator(id);
    }
  }, [id]);

  const setOnlineStatus = async (isOnline) => {
    try {
      console.log("Setting online status:", isOnline);
      const sdk = new MkdSDK();
      await sdk.SetOnlineStatus(isOnline);
    } catch (err) {
      console.error("Failed to set online status:", err);
    }
  };

  const startHeartbeat = () => {
    try {
      console.log("Starting heartbeat");
      const sdk = new MkdSDK();
      const eventSource = sdk.StartOnlineHeartbeat();
      heartbeatConnection.current = eventSource;

      eventSource.onmessage = () => {
        console.log("Heartbeat received");
      };

      eventSource.onerror = (error) => {
        console.error("Heartbeat error:", error);
        // Try to reconnect after a delay
        setTimeout(() => {
          if (heartbeatConnection.current) {
            heartbeatConnection.current.close();
            startHeartbeat();
          }
        }, 5000);
      };
    } catch (err) {
      console.error("Failed to start heartbeat:", err);
    }
  };

  const loadChats = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetChats();

      if (!response.error) {
        setChats(response.list || []);
        // Note: Notification checking is now handled globally by NotificationContext
      }
    } catch (err) {
      setError(err.message);
      showToast(globalDispatch, err.message, 5000, "error");
      // setTimeout(() => setError(""), 5000);
    } finally {
      setLoading(false);
    }
  };

  const loadChatMessages = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetChatMessages(id);

      if (!response.error) {
        setMessages(response.list || []);
        // setChatDetails(response.chat);
        // if (response.chat?.id?.value) {
        //   setId(response.chat.id.value);
        // }
      }
    } catch (err) {
      setError(err.message);
      showToast(globalDispatch, err.message, 5000, "error");
      // setTimeout(() => setError(""), 5000);
    } finally {
      setLoading(false);
    }
  };

  const subscribeToChat = () => {
    // Close previous subscription if any
    if (chatSubscription.current) {
      chatSubscription.current.close();
    }

    try {
      console.log("Subscribing to chat updates for:", id);
      const sdk = new MkdSDK();

      // Create subscription using the enhanced method
      const subscription = sdk.SubscribeToChatMessages(id);

      // Handle incoming messages
      subscription.onMessage((message) => {
        console.log("New message received:", message);
        // check chatDetails.partner.id.value === message.user_id.value
        if (chatDetails && chatDetails.partner &&
            chatDetails.partner.id.value === message.user.id.value) {
          setMessages(prev => [...prev, {
            ...message,
            is_sender: { value: false }
          }]);
        }
      });

      // Handle status updates
      subscription.onStatus((status) => {
        console.log("Status update received:", status);
        if (status.user_id.value === chatDetails.partner.id.value) {
          setChatStatus(status);
        }

        // Update chat details with online status
        if (chatDetails) {
          setChatDetails(prev => ({
            ...prev,
            status: { value: status.online?.value ? "active" : "offline" }
          }));
        }
      });

      // Handle errors
      subscription.onError((error) => {
        console.error("Chat subscription error:", error);
      });

      // Save subscription reference
      chatSubscription.current = subscription;

    } catch (err) {
      console.error("Failed to subscribe to chat updates:", err);
    }
  };



  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !id) return;

    try {
      setSendingMessage(true);
      const sdk = new MkdSDK();
      await sdk.SendChatMessage(id, { message: newMessage });
      setNewMessage("");
      setMessages(prev => [...prev, {
        id: { value: Date.now() },
        message: { value: newMessage },
        is_sender: { value: true },
        created_at: { value: new Date().toISOString() }
      }]);
    } catch (err) {
      setError(err.message);
      showToast(globalDispatch, err.message, 5000, "error");
      // setTimeout(() => setError(""), 5000);
    } finally {
      setSendingMessage(false);
    }
  };

  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

    const time = date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });

    // If message is from today, show only time
    if (messageDate.getTime() === today.getTime()) {
      return time;
    }

    // If message is from yesterday
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    if (messageDate.getTime() === yesterday.getTime()) {
      return `Yesterday ${time}`;
    }

    // If message is from this year, show month/day and time
    if (date.getFullYear() === now.getFullYear()) {
      return `${date.toLocaleDateString([], { month: 'short', day: 'numeric' })} ${time}`;
    }

    // For older messages, show full date and time
    return `${date.toLocaleDateString([], { month: 'short', day: 'numeric', year: 'numeric' })} ${time}`;
  };

  // Click outside to close search results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchInputRef.current && !searchInputRef.current.contains(event.target)) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle message input changes - notify typing status
  const handleMessageChange = (e) => {
    const value = e.target.value;
    setNewMessage(value);

    // Trigger typing indicator when user is typing
    if (typingIndicator.current && value.trim() !== "") {
      typingIndicator.current();
    }
  };

  return (
    <div className="flex h-full no-scrollbar overflow-hidden">
      {/* Chat List - Full height with black background */}
      <div className="w-[280px] h-[calc(100vh-62px)] border-r border-[#363636] bg-black overflow-y-auto no-scrollbar">
        <div className="p-4">

          <div className="space-y-1">
            {chats.map((chat) => (
              <button
                key={chat.id.value}
                onClick={() => {
                  setId(chat.id.value);
                  setChatDetails(chat);
                  // Initialize chat status with default values
                  setChatStatus({
                    online: { value: false },
                    typing: { value: false },
                    last_seen: { value: null }
                  });
                }}
                className={`flex w-full items-center gap-3 rounded-lg p-3 text-left transition-all
                  ${chat.id.value === Number(id) ? 'bg-[#2e7d32]/20 text-[#7dd87d]' : 'hover:bg-[#242424] text-[#eaeaea]'}`}
              >
                <div className="relative">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#363636] text-sm">
                    {chat.partner?.avatar?.value ? (
                      <img src={chat.partner?.avatar?.value} alt="Avatar" className="w-full h-full object-cover" />
                    ) : (
                      chat.partner?.name?.value?.charAt(0)
                    )}
                  </div>
                  {chat.unread_count.value > 0 && (
                    <span className="absolute -right-0.5 -top-0.5 h-2.5 w-2.5 rounded-full bg-red-500 ring-2 ring-[#1e1e1e]" />
                  )}
                </div>
                <div className="flex-1 overflow-hidden no-scrollbar">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">{ chat.partner?.name?.value}</h3>
                    <span className="text-[11px] text-[#b5b5b5]">
                      {new Date(chat.last_message_time.value).toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                  <p className="truncate text-xs text-[#b5b5b5]">
                    {chat.last_message.value}
                  </p>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Chat Content */}
      {id ? (
        <div className="flex flex-1 flex-col h-[calc(100vh-62px)]">
          {/* Chat Header */}
          {console.log("chatDerails",chatDetails)}
          {chatDetails && (
            <div className="border-b border-[#363636] bg-[#161616] p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-[#363636] text-sm">
                    {chatDetails.partner?.avatar?.value ? (
                      <img src={chatDetails.partner?.avatar?.value} alt="Avatar" className="w-full h-full object-cover" />
                    ) : (
                      chatDetails.partner?.name?.value?.charAt(0)
                    )}
                  </div>
                  <div>
                    <h2 className="text-sm font-medium text-[#eaeaea]">{chatDetails.partner?.name?.value}</h2>
                    <p className="text-xs text-[#b5b5b5]">
                      {chatStatus.online?.value ? (
                        <span className="text-green-500">● Online</span>
                      ) : chatStatus.last_seen?.value ? (
                        <span>Last seen: {new Date(chatStatus.last_seen.value).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}</span>
                      ) : (
                        'Offline'
                      )}
                      {chatStatus.typing?.value && (
                        <span className="ml-2 text-blue-500">typing...</span>
                      )}
                    </p>
                  </div>
                </div>
                <div>
                  <button
                  onClick={() => navigate(`/member/user/${chatDetails.partner?.id?.value}`)}
                  className="rounded-lg bg-[#2e7d32] px-4 py-2 text-xs text-[#eaeaea]">

                    View Profile
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Messages */}
          <div style={{ scrollbarWidth: 'none' }} className="flex-1 overflow-y-auto p-4 bg-[#1a1a1a] no-scrollbar">
            {loading ? (
              <div className="space-y-4">
                <SkeletonLoader className="h-10 w-3/4 rounded-lg" />
                <SkeletonLoader className="h-10 w-2/4 ml-auto rounded-lg" />
                <SkeletonLoader className="h-10 w-3/5 rounded-lg" />
              </div>
            ) : (
              <div className="space-y-4 no-scrollbar">
                {messages.map((message) => (
                  <div
                    key={message?.id?.value}
                    className={`flex ${message?.is_sender?.value ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[75%] rounded-lg px-4 py-2 ${
                        message?.is_sender?.value
                          ? 'bg-[#2e7d32] text-white'
                          : 'bg-[#242424] text-[#eaeaea]'
                      }`}
                    >
                      <p className="text-sm">{message?.message?.value}</p>
                      <p className="mt-1 text-right text-[11px] opacity-70">
                        {formatDateTime(message?.created_at?.value || new Date())}
                      </p>
                    </div>
                  </div>
                ))}
                <div ref={messageEndRef} />
              </div>
            )}
          </div>

          {/* Message Input */}
          <div className="border-t border-[#363636] bg-[#161616] p-4">
            <form onSubmit={handleSendMessage} className="flex gap-3">
              <input
                type="text"
                placeholder="Type a message..."
                className="flex-1 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"
                value={newMessage}
                onChange={handleMessageChange}
                disabled={sendingMessage}
              />
              <button
                type="submit"
                className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] disabled:opacity-50"
                disabled={sendingMessage || !newMessage.trim()}
              >
                Send
              </button>
            </form>
          </div>
        </div>
      ) : (
        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-medium text-[#eaeaea]">Select a chat or start a new conversation</h2>
            <p className="mt-2 text-[#b5b5b5]">Search for contacts to begin messaging</p>
          </div>
        </div>
      )}

      {/* {error && showToast(globalDispatch, error, 5000, "error")} */}
    </div>
  );
};

export default ChatPage;
