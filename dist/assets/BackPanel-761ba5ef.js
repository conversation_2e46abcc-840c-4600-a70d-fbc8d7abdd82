import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as p,d as s}from"./vendor-1c28ea83.js";import{G as n,ab as i}from"./index-826b1c0e.js";import"./index-82d4da4e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const _=()=>{const{state:{pageTitle:o,backLink:r},dispatch:t}=p.useContext(n),a=s(),m=()=>{i(t,"header","headerType"),i(t,"Page","pageTitle"),i(t,null,"backLink"),a(r??-1)};return e.jsxs("div",{className:"sticky top-0 z-[9999999] mt-0 flex min-h-[3.25rem] items-center gap-2 bg-black px-5 py-2 shadow-md",children:[e.jsx("div",{onClick:()=>{m()},className:"non_print_section text[1rem] w-[3.625rem] cursor-pointer font-inter font-[600] leading-[1.5rem] text-white",children:"<- Back"}),e.jsx("div",{className:"grow text-center text-lg capitalize text-white",children:o??"Page"})]})};export{_ as default};
