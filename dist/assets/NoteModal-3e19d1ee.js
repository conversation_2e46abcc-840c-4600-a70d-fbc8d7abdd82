import{j as t}from"./@react-google-maps/api-211df1ae.js";import{R as o,r as l}from"./vendor-1c28ea83.js";import{M as s,G as m,A as d}from"./index-826b1c0e.js";import{I as n}from"./index-632d14e3.js";import{M as c}from"./index-23518dfb.js";import{A as p}from"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const q=({onClose:r,isOpen:e,note:a})=>{new s;const{state:{updateModel:i,currentState:x},dispatch:h}=o.useContext(m);return o.useContext(d),l.useState([]),t.jsx(c,{isOpen:e,modalCloseClick:()=>r&&r(),title:"Note",modalHeader:!0,classes:{modalDialog:"!grid grid-rows-[auto_85%] !w-full !px-0 md:!w-[35.375rem] md:min-h-[50%] md:h-[50%] md:max-h-[50%] max-h-[50%] min-h-[50%]",modalContent:"!z-10 !px-0 overflow-hidden !pt-0",modal:"h-full"},children:t.jsx("div",{className:"h-full min-h-full",children:e?t.jsx(t.Fragment,{children:t.jsxs("div",{className:"relative mx-auto grid h-full max-h-full min-h-full w-full grow grid-cols-1 grid-rows-[90%_10%] rounded text-start !font-inter leading-snug tracking-wide",children:[t.jsx("div",{className:"w-full gap-5 overflow-y-auto pb-10 ",children:t.jsx("div",{className:"w-full space-y-5 px-5",children:t.jsx("div",{className:"text-justify font-inter text-lg font-medium leading-snug tracking-wider",children:a})})}),t.jsxs("div",{className:"relative flex gap-5 px-5",children:[t.jsx("div",{className:"grow"}),t.jsxs("div",{className:"flex h-fit w-[15.6875rem] items-center gap-[.75rem]",children:[t.jsx(p,{onClick:()=>r(),disabled:i==null?void 0:i.loading,className:"!hidden !grow !border-none !bg-soft-200 !text-sub-500",children:"Cancel"}),t.jsx(n,{type:"submit",loading:i==null?void 0:i.loading,disabled:i==null?void 0:i.loading,className:"!hidden !grow px-4 py-2 font-bold capitalize text-white",children:"Update and Close"})]})]})]})}):null})})};export{q as NoteModal,q as default};
