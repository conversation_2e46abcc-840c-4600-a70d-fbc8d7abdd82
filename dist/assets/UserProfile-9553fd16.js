import{j as s}from"./@react-google-maps/api-211df1ae.js";import{r as c,R as U}from"./vendor-1c28ea83.js";import{u as oe}from"./react-hook-form-eec8b32f.js";import{M as te,A as ie,a3 as le,G as ne,s as k,t as $,ab as J,L as me,o as re,ac as de}from"./index-826b1c0e.js";import{c as ce,a as _}from"./yup-f303108c.js";import{I as B}from"./index-632d14e3.js";import"./index-23518dfb.js";import{_ as Y}from"./qr-scanner-cf010ec4.js";import{C as Q,T as pe}from"./index-3bf596a0.js";import{M as R}from"./MkdInput-ef434b7d.js";import{A as he}from"./index-e2604cb4.js";import{P as X}from"./index-c752d551.js";import{U as fe}from"./index-cf40f7e3.js";import"./pdf-lib-623decea.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */c.lazy(()=>Y(()=>import("./ImageUpload-430dc8fb.js"),["assets/ImageUpload-430dc8fb.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-e2604cb4.js","assets/index-23518dfb.js"]));c.lazy(()=>Y(()=>import("./TitleDetail-ad8d2aa0.js"),["assets/TitleDetail-ad8d2aa0.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"]));const We=()=>{var Z,W;let x=new te;const{dispatch:w}=U.useContext(ie),[C,T]=c.useState(""),[i,M]=U.useState({}),[P,A]=c.useState(""),[E,r]=c.useState("");c.useState("");const[y,f]=c.useState(!1);c.useState("Profile");const[o,g]=c.useState({});c.useState([]);const[I,j]=c.useState({showModal:!1,modal:""}),{profile:l}=le(),{dispatch:p,state:{updatingUserImageModel:q}}=U.useContext(ne),D=ce({email:_().email().required(),first_name:_().required(),last_name:_().required(),phone:_().nullable(),photo:_().nullable(),dashboard_image:_().nullable(),company_name:_().nullable()}).required(),{register:b,handleSubmit:z,setError:F,setValue:v,watch:V,formState:{errors:L}}=oe({resolver:re(D)}),{dashboard_image:ee,photo:se}=V(),G=(e,a,N=!1)=>{let h=i;console.log(a),N?h[e]?h[e]=[...h[e],{file:a.files[0],tempFile:{url:URL.createObjectURL(a.files[0]),name:a.files[0].name,type:a.files[0].type}}]:h[e]=[{file:a.files[0],tempFile:{url:URL.createObjectURL(a.files[0]),name:a.files[0].name,type:a.files[0].type}}]:h[e]={file:a.files[0],name:a.files[0].name,type:a.files[0].type,tempURL:URL.createObjectURL(a.files[0])},M({...h})};async function H(){try{const e=await x.getCustomProfile();g(e),v("email",e==null?void 0:e.email),v("first_name",e==null?void 0:e.first_name),v("last_name",e==null?void 0:e.last_name),v("phone",e==null?void 0:e.phone),v("dashboard_image",e==null?void 0:e.dashboard_image),v("photo",e==null?void 0:e.photo),T(e==null?void 0:e.email),A(e==null?void 0:e.photo),r(e==null?void 0:e.dashboard_image),w({type:"UPDATE_PROFILE",payload:e})}catch(e){console.log("Error",e),$(w,e.response.data.message?e.response.data.message:e.message)}}const ae=async e=>{var a,N,h,S;g(e);try{if(f(!0),i&&i.photo&&((a=i.photo)!=null&&a.file)){let t=new FormData;t.append("file",(N=i.photo)==null?void 0:N.file);let n=await x.uploadImage(t);console.log("uploadResult"),console.log(n),e.photo=n.url,k(p,"Profile Photo Updated",1e3)}if(i&&i.dashboard_image&&((h=i.dashboard_image)!=null&&h.file)){let t=new FormData;t.append("file",(S=i.dashboard_image)==null?void 0:S.file);let n=await x.uploadImage(t);console.log("uploadResult"),console.log(n),e.dashboard_image=n.url,k(p,"Profile Photo Updated",1e3)}const d=await x.updateProfile({first_name:e.first_name||(o==null?void 0:o.first_name),last_name:e.last_name||(o==null?void 0:o.last_name),phone:e.phone||(o==null?void 0:o.phone),photo:e.photo||P,dashboard_image:e.dashboard_image||E});if(!(await de(p,w,"user",l==null?void 0:l.id,{first_name:e.first_name||(o==null?void 0:o.first_name),last_name:e.last_name||(o==null?void 0:o.last_name),phone:e.phone||(o==null?void 0:o.phone),photo:e.photo||P})).error)k(p,"Profile Updated",4e3),O();else{if(d.validation){const t=Object.keys(d.validation);for(let n=0;n<t.length;n++){const u=t[n];F(u,{type:"manual",message:d.validation[u]})}}O()}if(C!==e.email){const t=await x.updateEmail(e.email);if(!t.error)k(p,"Email Updated",1e3);else if(t.validation){const n=Object.keys(t.validation);for(let u=0;u<n.length;u++){const K=n[u];F(K,{type:"manual",message:t.validation[K]})}}O()}await H(),f(!1)}catch(d){f(!1),console.log("Error",d),F("email",{type:"manual",message:d.response.data.message?d.response.data.message:d.message}),$(w,d.response.data.message?d.response.data.message:d.message)}};c.useCallback(async e=>{var a,N,h,S,d;try{if(e!=null&&e.password&&((a=e==null?void 0:e.password)==null?void 0:a.length)>0){const m=await x.updatePassword(e.password);if(!m.error)k(p,"Password Updated",5e3,"success");else if(m.validation){const t=Object.keys(m.validation);for(let n=0;n<t.length;n++){const u=t[n];F(u,{type:"manual",message:m.validation[u]})}}}}catch(m){const t=(h=(N=m==null?void 0:m.response)==null?void 0:N.data)!=null&&h.message?(d=(S=m==null?void 0:m.response)==null?void 0:S.data)==null?void 0:d.message:m==null?void 0:m.message;k(p,t,5e3,"error"),$(w,t)}},[]),U.useEffect(()=>{p({type:"SETPATH",payload:{path:"profile"}}),H()},[]);const O=(e="")=>{j(a=>({...a,modal:"",showModal:!1}))};return U.useEffect(()=>{J(p,"backpanel","headerType"),J(p,"Profile","pageTitle"),["user"].includes(l==null?void 0:l.role)&&v("company_name",l==null?void 0:l.company_name)},[l==null?void 0:l.role]),s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"bg-soft-200 !font-inter mx-auto flex h-full max-h-full min-h-full flex-col items-center justify-start gap-5 overflow-auto rounded leading-snug tracking-wide shadow-md md:p-5",children:s.jsxs("form",{onSubmit:z(ae),className:"!w-full space-y-5 md:!w-[75%]",children:[s.jsxs(Q,{children:[["user"].includes(l==null?void 0:l.role)&&s.jsxs("section",{className:"space-y-5",children:[s.jsx("div",{children:s.jsx(X,{image:((Z=i==null?void 0:i.dashboard_image)==null?void 0:Z.tempURL)||ee||E,title:"Dashboard Logo",name:"dashboard_image",onUpload:G})}),s.jsx("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-4",children:s.jsx(R,{label:"Company",name:"company_name",register:b,errors:L,disabled:!0})}),s.jsx("hr",{})]}),s.jsxs("section",{className:"grid grid-cols-1 gap-[1.5rem] ",children:[s.jsxs(pe,{className:"!border-0 !p-0 !shadow-none ",children:[" ","User Details"," "]}),s.jsx("div",{children:s.jsx(X,{image:((W=i==null?void 0:i.photo)==null?void 0:W.tempURL)||se||P,title:"Profile Image",name:"photo",onUpload:G})}),s.jsxs("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-4",children:[s.jsx(R,{label:"First Name",name:"first_name",register:b,errors:L}),s.jsx(R,{label:"Last Name",name:"last_name",register:b,errors:L})]}),s.jsxs("div",{className:"grid grid-cols-1 gap-[1.5rem] md:grid-cols-4",children:[s.jsx(R,{label:"Phone",name:"phone",register:b,errors:L}),s.jsx(R,{label:"Email",name:"email",register:b,errors:L})]}),s.jsx(me,{children:s.jsx(he,{className:"!shadow-0 !text-primary w-fit !border-0 !bg-white font-[700]",showPlus:!1,onClick:()=>{j(e=>({...e,modal:"password",showModal:!0}))},children:"Change Password"})})]})]}),s.jsx(Q,{className:"!bg-transparent !shadow-none md:p-0",children:s.jsx(B,{type:"submit",loading:y,disabled:y,className:"w-full rounded px-4 py-2  font-bold text-white md:w-[auto]",children:"Save Changes"})})]})}),s.jsx(fe,{isOpen:I.showModal&&I.modal==="password",onClose:O})]})},Ke=x=>{var q,D;const{title:w,label:C,buttonName:T,isOpen:i,onClose:M,handleSubmit:P,onSubmit:A,register:E,id:r,submitLoading:y,errors:f,defaultValues:o}=x,[g,I]=c.useState(!1),[j,l]=c.useState({email:""}),p=b=>z=>{b==="email"&&l({...j,[b]:z.target.value})};return s.jsx("div",{className:"fixed inset-0 z-10 overflow-y-auto",children:s.jsx("div",{className:`fixed inset-0 z-10 overflow-y-auto ${i?"block":"hidden"}`,children:s.jsxs("div",{className:"flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0",children:[s.jsx("div",{className:"fixed inset-0 transition-opacity",children:s.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),s.jsx("span",{className:"hidden sm:inline-block sm:h-screen sm:align-middle","aria-hidden":"true",children:"​"}),s.jsxs("div",{className:"inline-block transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("div",{className:"text-lg font-semibold leading-6 text-gray-900",children:w}),s.jsx("button",{className:"text-gray-500 hover:text-gray-700 focus:outline-none",onClick:M,children:s.jsx("svg",{className:"h-6 w-6",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M6 18L18 6M6 6l12 12"})})})]}),s.jsxs("form",{onSubmit:P(A),className:"max-w-lg",children:[g===!0&&s.jsxs("div",{className:"mt-3 flex",children:[s.jsx("div",{className:"mr-2",children:s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",children:s.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M10.0003 1.66663C5.39795 1.66663 1.66699 5.39759 1.66699 9.99996C1.66699 14.6023 5.39795 18.3333 10.0003 18.3333C14.6027 18.3333 18.3337 14.6023 18.3337 9.99996C18.3337 5.39759 14.6027 1.66663 10.0003 1.66663ZM8.33366 9.16663C8.33366 8.82145 8.61348 8.54163 8.95866 8.54163H10.0003C10.3455 8.54163 10.6253 8.82145 10.6253 9.16663L10.6253 13.5416C10.6253 13.8868 10.3455 14.1666 10.0003 14.1666C9.65515 14.1666 9.37533 13.8868 9.37533 13.5416L9.37532 9.79163H8.95866C8.61348 9.79163 8.33366 9.5118 8.33366 9.16663ZM10.0003 6.04163C9.65515 6.04163 9.37533 6.32145 9.37533 6.66663C9.37533 7.0118 9.65515 7.29163 10.0003 7.29163C10.3455 7.29163 10.6253 7.0118 10.6253 6.66663C10.6253 6.32145 10.3455 6.04163 10.0003 6.04163Z",fill:"#4F46E5"})})}),s.jsxs("div",{children:[s.jsx("p",{className:"mb-1	text-sm	font-medium text-gray-600",children:"We've send an email to:"}),s.jsx("p",{className:"mb-2	text-sm	font-semibold text-gray-900",children:j==null?void 0:j.email}),s.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"In order to complete the email update click the confirmation link."}),s.jsx("p",{className:"mb-2	text-sm	font-medium text-gray-600",children:"(the link expires in 24 hours)"})]})]}),g===!1&&["first_name","last_name","phone"].includes(r)&&s.jsxs("div",{className:"mt-3",children:[s.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:C}),s.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:r,type:"text",placeholder:`Enter ${C}`,name:r,...E(r)}),s.jsx("p",{className:"text-xs italic text-red-500",children:(q=f==null?void 0:f.id)==null?void 0:q.message})]}),g===!1&&r==="email"&&s.jsxs("div",{className:"mt-3",children:[s.jsx("label",{htmlFor:"firstName",className:"mb-1 block text-sm font-medium text-gray-700",children:C}),s.jsx("input",{className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:r,type:"text",placeholder:`Enter ${C}`,name:r,...E(r),onChange:p("email")}),s.jsx("p",{className:"text-xs italic text-red-500",children:(D=f==null?void 0:f.id)==null?void 0:D.message})]}),s.jsxs("div",{className:"mt-4 flex justify-between",children:[s.jsx("button",{className:"mr-2 w-full rounded-md border border-gray-300 px-4 py-2 text-gray-700	",onClick:M,children:"Cancel"}),(r==="first_name"||r==="last_name"||r==="phone"||g===!0)&&s.jsx(B,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:y,disabled:y,children:T}),r==="email"&&!g&&s.jsx(B,{className:"focus:shadow-outline w-full rounded-md bg-indigo-500 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:y,disabled:y,onClick:()=>I(!0),children:"Submit"})]})]})]})]})})})};export{Ke as EditInfoModal,We as default};
