import{j as e}from"./@react-google-maps/api-211df1ae.js";import{d as U,r as o}from"./vendor-1c28ea83.js";import{G as V,M as p,s as m,T as W}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const f=()=>e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13.3334 4L6.00008 11.3333L2.66675 8",stroke:"#2E7D32",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),g=()=>e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12 4L4 12",stroke:"#E53935",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M4 4L12 12",stroke:"#E53935",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),ue=()=>{U();const{dispatch:r}=o.useContext(V),[i,c]=o.useState([{email:"",communityId:""}]),[j,d]=o.useState(!1),[y,x]=o.useState(""),[k,E]=o.useState([]),[v,S]=o.useState([]);o.useState([]),o.useEffect(()=>{h(),L()},[]);const h=async()=>{try{d(!0);const s=await new p().GetAffiliateInvites();s.error?x(s.message||"Failed to load affiliate invites"):E(s.list)}catch(t){x(t.message),m(r,"error",t.message)}finally{d(!1)}},L=async()=>{try{const s=await new p().GetUserCommunities();s.error?console.error("Failed to load communities:",s.message):S(s.list)}catch(t){console.error("Failed to load communities:",t)}},A=()=>{i.length<5&&c([...i,{email:"",communityId:""}])},$=t=>{const s=i.filter((a,n)=>n!==t);c(s)},P=(t,s)=>{const a=[...i];a[t]={...a[t],email:s},c(a)},R=(t,s)=>{const a=[...i];a[t]={...a[t],communityId:s},c(a)},_=async t=>{t.preventDefault();const s=i.filter(a=>a.email.trim()&&a.communityId);if(s.length===0){m(r,"error","Please enter at least one email address and select a community");return}try{d(!0);const a=new p,n=s.map(l=>a.SendAffiliateInvite({email:l.email.trim(),community_id:l.communityId}));await Promise.all(n),m(r,`Successfully sent ${s.length} invitation${s.length>1?"s":""}`),c([{email:"",communityId:""}]),h()}catch(a){x(a.message),m(r,"error",a.message)}finally{d(!1)}};function F(t){const s=[".xlsx",".csv",".xls"],a=t.slice((t.lastIndexOf(".")-1>>>0)+2);return s.includes("."+a.toLowerCase())}const M=t=>t.split(/\r\n|\n|\r/).map(s=>s.split(",")),D=()=>{const t=document.createElement("input");t.type="file",t.click(),t.addEventListener("change",s=>{const a=s.target.files[0];if(!F(a.name)){m(r,`${a.type} is not a valid file.`,2e3,"error");return}const n=new FileReader;n.onload=l=>{const N=l.target.result;G(N)},n.readAsText(a)})},G=t=>{const s=M(t),a=[];let n=!1,l="Incorrect data format";if((s[0][0].toLowerCase()==="email"?s.slice(1):s).forEach((b,w)=>{if(b.length<2)return;const u=b[0].trim();if(!u||!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u)){l=`Invalid email "${u}" at row ${w+1}`,n=!0;return}const I=b[1].trim(),C=v.find(O=>O.name.value.toLowerCase()===I.toLowerCase());if(!C){l=`Community "${I}" at row ${w+1} does not exist`,n=!0;return}a.push({email:u,communityId:C.id.value})}),n){m(r,l,2e3,"error");return}const B=a.slice(0,5);T(B)},T=async t=>{try{d(!0);const s=new p,a=t.map(n=>s.SendAffiliateInvite({email:n.email,community_id:n.communityId}));await Promise.all(a),m(r,`Successfully sent ${t.length} invitation${t.length>1?"s":""}`,2e3,"success"),c([{email:"",communityId:""}]),h()}catch(s){x(s.message),m(r,s.message,2e3,"error")}finally{d(!1)}};return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4",children:[y&&e.jsx(W,{message:y,type:"error"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-[#eaeaea]",children:"Affiliate Program"}),e.jsx("p",{className:"text-[#b5b5b5]",children:"Help your community managers by bringing people to your community and get 10% of their monthly subscription fee"})]}),e.jsxs("div",{className:"mb-8 bg-[#161616] p-4",children:[e.jsx("h2",{className:"mb-3 text-lg font-semibold text-[#eaeaea]",children:"Invite New Affiliates"}),e.jsxs("form",{onSubmit:_,className:"space-y-4",children:[e.jsx("div",{className:"space-y-3",children:i.map((t,s)=>e.jsxs("div",{className:"flex gap-3",children:[e.jsx("input",{type:"email",placeholder:"Add people using their emails",value:t.email,onChange:a=>P(s,a.target.value),className:"flex-1 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"}),e.jsxs("select",{value:t.communityId,onChange:a=>R(s,a.target.value),className:"w-64 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none",children:[e.jsx("option",{value:"",children:"Select Community"}),v.map(a=>e.jsx("option",{value:a.id.value,children:a.name.value},a.id.value))]}),s>0&&e.jsx("button",{type:"button",onClick:()=>$(s),className:"rounded-lg border border-[#e53935] px-3 text-sm text-[#e53935] hover:bg-[#e53935]/10",children:"Remove"})]},s))}),e.jsxs("div",{className:"flex items-center gap-3",children:[i.length<5&&e.jsx("button",{type:"button",onClick:A,className:"rounded-lg border border-[#2e7d32] px-4 py-2 text-sm text-[#2e7d32] hover:bg-[#2e7d32]/10",children:"+ Add Another Email"}),e.jsx("button",{type:"button",onClick:D,className:"rounded-lg border border-[#2e7d32] px-4 py-2 text-sm text-[#2e7d32] hover:bg-[#2e7d32]/10",children:"Upload CSV"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{type:"submit",disabled:j||!i.some(t=>t.email.trim()&&t.communityId),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#2e7d32]/90 disabled:opacity-50",children:j?"Sending...":"Send Invites"})})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("p",{className:"text-xs text-[#b5b5b5] mb-2",children:"Once the emails are enrolled, and they pay the community subscription fee, we will give the inviter 10% of their subscription fee."}),e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:'CSV format: email,community_name (e.g., "<EMAIL>,Community Name")'})]})]}),e.jsxs("div",{className:"bg-[#161616] p-4",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Your Invites"}),e.jsx("div",{className:"overflow-x-auto rounded-lg border border-[#363636]",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-[#363636] bg-[#242424]",children:[e.jsx("th",{className:"p-3 text-left text-sm font-medium text-[#b5b5b5]",children:"Name"}),e.jsx("th",{className:"p-3 text-left text-sm font-medium text-[#b5b5b5]",children:"Email"}),e.jsx("th",{className:"p-3 text-center text-sm font-medium text-[#b5b5b5]",children:"Community"}),e.jsx("th",{className:"p-3 text-center text-sm font-medium text-[#b5b5b5]",children:"Signup"}),e.jsx("th",{className:"p-3 text-center text-sm font-medium text-[#b5b5b5]",children:"Onboarding"}),e.jsx("th",{className:"p-3 text-center text-sm font-medium text-[#b5b5b5]",children:"Subscription"}),e.jsx("th",{className:"p-3 text-center text-sm font-medium text-[#b5b5b5]",children:"Status"}),e.jsx("th",{className:"p-3 text-center text-sm font-medium text-[#b5b5b5]",children:"Commission"})]})}),e.jsx("tbody",{children:k.map(t=>e.jsxs("tr",{className:"border-b border-[#363636]",children:[e.jsx("td",{className:"p-3 text-sm text-[#eaeaea]",children:t.name.value}),e.jsx("td",{className:"p-3 text-sm text-[#eaeaea]",children:t.email.value}),e.jsx("td",{className:"p-3 text-center text-sm text-[#eaeaea]",children:t.community.value}),e.jsx("td",{className:"p-3 text-center text-sm text-[#eaeaea]",children:t.signup_completed.value?e.jsx("span",{className:"inline-flex items-center justify-center text-[#2e7d32]",children:e.jsx(f,{})}):e.jsx("span",{className:"inline-flex items-center justify-center text-[#e53935]",children:e.jsx(g,{})})}),e.jsx("td",{className:"p-3 text-center",children:t.onboarding_completed.value?e.jsx("span",{className:"inline-flex items-center justify-center text-[#2e7d32]",children:e.jsx(f,{})}):e.jsx("span",{className:"inline-flex items-center justify-center text-[#e53935]",children:e.jsx(g,{})})}),e.jsx("td",{className:"p-3 text-center",children:t.subscription_active.value?e.jsx("span",{className:"inline-flex items-center justify-center text-[#2e7d32]",children:e.jsx(f,{})}):e.jsx("span",{className:"inline-flex items-center justify-center text-[#e53935]",children:e.jsx(g,{})})}),e.jsx("td",{className:"p-3 text-center",children:e.jsx("span",{className:`rounded-full px-3 py-1 text-xs ${t.status.value==="active"?"bg-[#2e7d32]/20 text-[#7dd87d]":"bg-[#363636] text-[#b5b5b5]"}`,children:t.status.value==="active"?"Active":"Pending"})}),e.jsx("td",{className:"p-3 text-center text-sm text-[#eaeaea]",children:t.commission.value>0?"$"+t.commission.value:"0%"})]},t.id.value))})]})})]})]})};export{ue as default};
