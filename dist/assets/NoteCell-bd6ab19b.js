import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r as m}from"./vendor-1c28ea83.js";import{az as i}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const I=m.memo(({value:o,showNote:r})=>t.jsxs("button",{onClick:()=>r(o),type:"button",className:"flex items-center gap-2 ",children:[t.jsx(i,{className:"h-[1.0313rem] w-[1.0313rem]"}),"View"]}));export{I as default};
