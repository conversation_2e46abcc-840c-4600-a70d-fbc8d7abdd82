import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r,R as o,d as j}from"./vendor-1c28ea83.js";import{A as S,G as D,a3 as v,a4 as u,t as E,a5 as N}from"./index-826b1c0e.js";import{M as m}from"./index-53bbb473.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const n=[{header:"Row",accessor:"row"},{header:"Date",accessor:"create_at",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Order Number",accessor:"order_number",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Items",accessor:"items",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Quantity",accessor:"quantity",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}},{header:"Order Status",accessor:"order_status",isSorted:!0,isSortedDesc:!1,mappingExist:!1,mappings:{}}],se=()=>{const a=r.useRef(null),{state:R,dispatch:p}=r.useContext(S),{state:{dashboard_image:l},dispatch:h}=r.useContext(D),[T,f]=o.useState([]);o.useState(!1);const{profile:e}=v(),c=j();return o.useEffect(()=>{h({type:"SETPATH",payload:{path:"dashboard"}});async function g(d,i,_){let x=new N;try{let s=n.filter(y=>y.isSorted);const w=await x.getPaginate("order",{page:d,size:i,order:s.length?s[0].accessor:"",direction:s.length?s[0].isSortedDesc?"DESC":"ASC":"",filter:[]}),{list:b,total:k,limit:C,num_pages:F,page:P}=w;f(b)}catch(s){console.log("ERROR",s),E(p,s.message)}}(function(){const i=setTimeout(async()=>{await g(1)},700);return()=>{clearTimeout(i)}})()},[]),r.useEffect(()=>{a.current&&(a.current.style.backgroundImage=`url(${l})`,a.current.style.backgroundRepeat="none",a.current.style.backgroundSize="100% 100%")},[l,a==null?void 0:a.current]),t.jsx(r.Fragment,{children:t.jsxs("div",{className:"mx-auto flex h-full max-h-full min-h-full w-full max-w-full flex-col overflow-auto rounded bg-white ",children:[t.jsxs("div",{className:"flex justify-between border-b border-gray-200 bg-white px-2",children:[t.jsx("div",{className:"flex h-[3.5rem] items-center border-b border-gray-200 px-2 py-5 text-[1.25rem] font-bold ",children:"Dashboard"}),l&&t.jsx("div",{ref:a,className:"flex w-[11.3125rem] items-center justify-center",children:t.jsx("img",{src:l,alt:"dashboard_image",className:"object-fill"})})]}),t.jsx("div",{className:"mx-auto flex h-fit max-h-fit min-h-fit w-full max-w-full flex-col justify-between gap-12 rounded bg-white px-2",children:e!=null&&e.id?t.jsxs("div",{className:"flex h-fit w-full flex-col items-start justify-center gap-2 md:flex-row",children:[t.jsx("div",{className:"w-full rounded-[.625rem] md:w-1/2",children:t.jsx(m,{showSearch:!1,columns:n,hasFilter:!1,tableRole:"user",table:"order",actionId:"id",tableTitle:"Latest Orders",join:["user","division","campaign","warehouse"],defaultFilter:[`customer,eq,${e!=null&&e.is_company?e==null?void 0:e.id:e==null?void 0:e.company_id}`],actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},delete:{show:!1,action:null,multiple:!0},orders:{show:!0,type:"static",action:()=>c(`/${e==null?void 0:e.role}/orders?view=pending`),children:t.jsx(t.Fragment,{children:t.jsx(u,{})}),className:"!gap-2 !bg-transparent !text-black !border-0 !border-gray-200"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:10,showPagination:!1})}),t.jsx("div",{className:"w-full rounded-[.625rem] md:w-1/2",children:t.jsx(m,{defaultColumns:n,hasFilter:!1,tableRole:"user",table:"receipts",tableTitle:"Latest Receipts",actionId:"id",join:["user","division","campaign","warehouse"],defaultFilter:[`customer,eq,${e!=null&&e.is_company?e==null?void 0:e.id:e==null?void 0:e.company_id}`],actions:{view:{show:!1,action:null,multiple:!1},select:{show:!1,action:null,multiple:!1},delete:{show:!1,action:null,multiple:!0},receipts:{show:!0,type:"static",action:()=>c(`/${e==null?void 0:e.role}/receipts`),children:t.jsx(t.Fragment,{children:t.jsx(u,{})}),className:"!gap-2 !bg-transparent !text-black !border-0 !border-gray-200"},add:{show:!1,multiple:!0,children:"+ Add"},export:{show:!1,action:null,multiple:!0}},defaultPageSize:10,showPagination:!1,showSearch:!1})})]}):null})]})})};export{se as default};
