import{j as e}from"./@react-google-maps/api-211df1ae.js";import{aw as u,ao as j}from"./index-826b1c0e.js";import{r as x}from"./vendor-1c28ea83.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const p=({updatePageSize:t,pageSize:s,startSize:d,multiplier:a,canChangeLimit:l=!0})=>{const o=x.useMemo(()=>d,[d]),n=x.useMemo(()=>a,[a]);return e.jsx(e.Fragment,{children:o?e.jsx("select",{disabled:!l,className:`${l?"":"appearance-none bg-none px-2"} h-[2.5rem] max-h-[2.5rem] w-fit min-w-fit self-end rounded-md border-neutral-300 py-[.375rem] bg-black`,value:s,onChange:c=>{t(Number(c.target.value))},children:Array.from({length:6}).map((c,m)=>e.jsxs("option",{value:Number(o)+n*m,children:[l?"Show":"Showing"," ",Number(o)+n*m]},Number(o)+n*m))}):null})},U=({currentPage:t,pageCount:s,pageSize:d,canPreviousPage:a,canNextPage:l,updatePageSize:o,previousPage:n,nextPage:c,startSize:m=500,multiplier:f=100,updateCurrentPage:b,canChangeLimit:h=!0})=>{const[i,w]=x.useState(!1);return e.jsx(e.Fragment,{children:e.jsxs("div",{className:"grid h-fit grid-cols-1 grid-rows-2 flex-col items-center justify-between gap-[1.5rem] pl-2 md:grid-cols-[auto_1fr_auto] md:grid-rows-1 md:flex-row",children:[e.jsxs("div",{className:"flex w-full justify-between md:hidden",children:[e.jsx("div",{className:"",children:e.jsxs("span",{children:["Page"," ",e.jsxs("strong",{children:[+t," of ",s]})," "]})}),e.jsx("div",{className:"",children:e.jsx(p,{pageSize:d,multiplier:f,startSize:m,updatePageSize:o,canChangeLimit:h})})]}),e.jsx("div",{className:"hidden w-fit min-w-fit max-w-fit justify-between whitespace-nowrap md:block",children:e.jsxs("span",{children:["Page"," ",e.jsxs("strong",{children:[+t," of ",s]})," "]})}),e.jsxs("div",{className:`scrollbar-hide flex h-[2.5rem] w-full min-w-full max-w-full items-center gap-[.375rem] overflow-x-auto px-5  ${i?"justify-start":"justify-start md:justify-center"}`,children:[e.jsx("button",{type:"button",onClick:n,disabled:!a,className:"flex h-[2rem] w-[2rem] items-center justify-center rounded px-2",children:e.jsx(u,{className:"rotate-180"})}),e.jsx("button",{type:"button",disabled:!0,className:"flex h-[2rem] w-[2rem] items-center justify-center rounded px-2",children:e.jsx(j,{className:"rotate-180"})}),i?e.jsx("button",{type:"button",className:"h-[2rem] w-[2rem] rounded border border-gray-200 px-2 shadow-md",onClick:()=>w(!1),children:"..."}):null,e.jsx("div",{className:"flex w-fit justify-start space-x-2 ",children:s!==void 0&&Array.from({length:Number(s)}).map((N,y)=>{const r=y+1;if(!i&&s<=5||!i&&s<=7)return e.jsx("button",{type:"button",disabled:r===t,className:`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${t===r?"bg-black":""}`,onClick:()=>b(r),children:r},r);if(!i&&s>5&&r<=5)return e.jsx("button",{type:"button",disabled:r===t,className:`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${t===r?"bg-black":""}`,onClick:()=>b(r),children:r},r);if(s>5&&s>=8&&r>5&&r<7&&!i)return e.jsx("button",{type:"button",disabled:r===t,className:`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded bg-black border border-gray-200 px-2 shadow-md ${t===r?"bg-weak-100":""}`,onClick:()=>w(!0),children:"..."},r);if(!i&&s>5&&s>=8&&r===7)return e.jsx("button",{type:"button",disabled:r===t,className:`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${t===r?"bg-black":""}`,onClick:()=>b(r),children:s},r);if(i&&s>5&&s>=8&&r>5)return e.jsx("button",{type:"button",disabled:r===t,className:`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${t===r?"bg-black":""}`,onClick:()=>b(r),children:r},r)})}),e.jsx("button",{type:"button",disabled:!0,className:"flex h-[2rem] w-[2rem] items-center justify-center rounded px-2",children:e.jsx(j,{})}),e.jsx("button",{type:"button",onClick:c,disabled:!l,className:"flex h-[2rem] w-[2rem] items-center justify-center rounded px-2",children:e.jsx(u,{})})]}),e.jsx("div",{className:"hidden md:block",children:e.jsx(p,{pageSize:d,updatePageSize:o,multiplier:f,startSize:m,canChangeLimit:h})})]})})};export{U as default};
