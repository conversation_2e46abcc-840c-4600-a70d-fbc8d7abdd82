import{j as r}from"./@react-google-maps/api-211df1ae.js";import{r as n}from"./vendor-1c28ea83.js";import{M as l}from"./MkdInput-ef434b7d.js";import{c,d}from"./index.esm-4be700bd.js";import"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const D=({register:o,errors:s,name:e="password",label:i="Password",className:a,required:p=!0})=>{const[m,t]=n.useState("password");return r.jsxs("div",{children:[r.jsxs("label",{className:"mb-2 block cursor-pointer text-[.875rem] font-bold",htmlFor:e,children:[i,p&&r.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]}),r.jsxs("div",{className:`flex h-fit w-full appearance-none items-center rounded-[.625rem] border pr-2 font-inter leading-tight text-black shadow focus:outline-none focus:ring-0  ${a}`,children:[r.jsx("div",{className:"grow",children:r.jsx(l,{type:m,name:e,className:"w-full !border-0 !shadow-none",errors:s,register:o})}),r.jsx("div",{className:"h-[.755rem] max-h-[.755rem] min-h-[.755rem] w-[1.0294rem] min-w-[1.0294rem] max-w-[1.0294rem]",children:m==="password"?r.jsx(c,{className:"h-[.755rem] max-h-[.755rem] min-h-[.755rem] w-[1.0294rem] min-w-[1.0294rem] max-w-[1.0294rem] cursor-pointer text-gray-400",onClick:()=>t("text")}):r.jsx(d,{className:"h-[.755rem] max-h-[.755rem] min-h-[.755rem] w-[1.0294rem] min-w-[1.0294rem] max-w-[1.0294rem] cursor-pointer text-gray-400",onClick:()=>t("password")})})]})]})};export{D as default};
