import{j as t}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{m as s}from"./framer-motion-dd957f90.js";const o={start:{transition:{staggerChildren:.2}},end:{transition:{staggerChildren:.2}}},a={start:{y:"0%"},end:{y:"100%"}},i={duration:.4,yoyo:1/0,ease:"easeIn"};function p({dotsClasses:r,size:d,style:e}){const n="block w-[9px] h-[9px] bg-primary rounded-md shrink-0 "+r;return t.jsxs(s.div,{variants:o,className:"flex w-[40px] items-center justify-between pb-[10px]",initial:"start",animate:"end",style:{...e},children:[t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i}),t.jsx(s.span,{className:n,variants:a,transition:i})]})}export{p as default};
