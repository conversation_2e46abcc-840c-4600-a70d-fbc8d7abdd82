import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as a,d as D}from"./vendor-1c28ea83.js";import{u as B}from"./react-hook-form-eec8b32f.js";import{G as v,M as f,s as c,A as P,o as M}from"./index-826b1c0e.js";import{c as R,a as x,b as I}from"./yup-f303108c.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const q=({isOpen:w,onClose:h,onSuccess:g})=>{const[u,b]=a.useState(!1),{dispatch:j}=a.useContext(v),C=async()=>{try{b(!0);const d=new f,o=new URL(window.location.href),i=`${o.origin}${o.pathname}?stripe_return=true`,s=await d.callRawAPI("/v1/api/dealmaker/user/stripe/onboarding",{return_url:i},"POST");if(console.log("Stripe onboarding response:",s),s&&s.url)window.location.href=s.url;else throw new Error("Failed to get Stripe onboarding link")}catch(d){console.error("Stripe Connect error:",d),c(j,d.message||"Failed to connect Stripe account",5e3,"error")}finally{b(!1)}};return w?e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75",children:e.jsxs("div",{className:"w-[500px] rounded-lg bg-[#161616] p-6",children:[e.jsxs("div",{className:"mb-6 text-center",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-[#2e7d32]",children:e.jsx("svg",{className:"h-8 w-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Connect Your Stripe Account"}),e.jsx("p",{className:"mt-2 text-sm text-[#b5b5b5]",children:"To receive payments and withdraw your earnings, you need to connect your Stripe account. This is a one-time setup that enables secure payments."})]}),e.jsxs("div",{className:"mb-6 space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-[#2e7d32]",children:e.jsx("svg",{className:"h-3 w-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Secure payment processing"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-[#2e7d32]",children:e.jsx("svg",{className:"h-3 w-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Direct deposits to your bank account"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-[#2e7d32]",children:e.jsx("svg",{className:"h-3 w-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Real-time earnings tracking"})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-[#2e7d32]",children:e.jsx("svg",{className:"h-3 w-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Industry-standard security"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("button",{onClick:C,disabled:u,className:"w-full rounded bg-[#2e7d32] py-3 text-sm font-medium text-[#eaeaea] hover:bg-[#266d2a] disabled:opacity-50",children:u?"Connecting...":"Connect Stripe Account"}),e.jsx("button",{onClick:h,disabled:u,className:"w-full rounded border border-[#404040] bg-transparent py-3 text-sm font-medium text-[#b5b5b5] hover:bg-[#242424] disabled:opacity-50",children:"Skip for now"}),e.jsx("p",{className:"text-center text-xs text-[#b5b5b5]",children:"You'll be redirected to Stripe to securely connect your account. You can set this up later from your payment dashboard."})]})]})}):null},F=()=>e.jsx("svg",{width:"30",height:"24",viewBox:"0 0 30 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{id:"Frame",children:e.jsx("path",{id:"Vector",d:"M15.1594 3.99375L10.6219 7.66875C9.86719 8.27812 9.72188 9.375 10.2938 10.1578C10.8984 10.9922 12.075 11.1562 12.8859 10.5234L17.5406 6.90469C17.8688 6.65156 18.3375 6.70781 18.5953 7.03594C18.8531 7.36406 18.7922 7.83281 18.4641 8.09062L17.4844 8.85L24 14.85V6H23.9672L23.7844 5.88281L20.3812 3.70312C19.6641 3.24375 18.825 3 17.9719 3C16.95 3 15.9562 3.35156 15.1594 3.99375ZM16.2281 9.825L13.8047 11.7094C12.3281 12.8625 10.1859 12.5625 9.07969 11.0437C8.03906 9.61406 8.30156 7.61719 9.675 6.50625L13.575 3.35156C13.0312 3.12187 12.4453 3.00469 11.85 3.00469C10.9688 3 10.1109 3.2625 9.375 3.75L6 6V16.5H7.32187L11.6063 20.4094C12.525 21.2484 13.9453 21.1828 14.7844 20.2641C15.0422 19.9781 15.2156 19.6453 15.3047 19.2984L16.1016 20.0297C17.0156 20.8687 18.4406 20.8078 19.2797 19.8937C19.4906 19.6641 19.6453 19.3969 19.7438 19.1203C20.6531 19.7297 21.8906 19.6031 22.6547 18.7687C23.4937 17.8547 23.4328 16.4297 22.5187 15.5906L16.2281 9.825ZM0.75 6C0.3375 6 0 6.3375 0 6.75V16.5C0 17.3297 0.670312 18 1.5 18H3C3.82969 18 4.5 17.3297 4.5 16.5V6H0.75ZM2.25 15C2.44891 15 2.63968 15.079 2.78033 15.2197C2.92098 15.3603 3 15.5511 3 15.75C3 15.9489 2.92098 16.1397 2.78033 16.2803C2.63968 16.421 2.44891 16.5 2.25 16.5C2.05109 16.5 1.86032 16.421 1.71967 16.2803C1.57902 16.1397 1.5 15.9489 1.5 15.75C1.5 15.5511 1.57902 15.3603 1.71967 15.2197C1.86032 15.079 2.05109 15 2.25 15ZM25.5 6V16.5C25.5 17.3297 26.1703 18 27 18H28.5C29.3297 18 30 17.3297 30 16.5V6.75C30 6.3375 29.6625 6 29.25 6H25.5ZM27 15.75C27 15.5511 27.079 15.3603 27.2197 15.2197C27.3603 15.079 27.5511 15 27.75 15C27.9489 15 28.1397 15.079 28.2803 15.2197C28.421 15.3603 28.5 15.5511 28.5 15.75C28.5 15.9489 28.421 16.1397 28.2803 16.2803C28.1397 16.421 27.9489 16.5 27.75 16.5C27.5511 16.5 27.3603 16.421 27.2197 16.2803C27.079 16.1397 27 15.9489 27 15.75Z",fill:"#7DD87D"})})}),ie=()=>{const w=R({first_name:x().required("First name is required"),last_name:x().required("Last name is required"),email:x().email("Invalid email").required("Email is required"),password:x().min(8,"Password must be at least 8 characters").required("Password is required"),confirm_password:x().oneOf([I("password")],"Passwords must match").required("Please confirm your password"),industry:x().required("Please select your industry")}),[h,g]=a.useState(!1),[u,b]=a.useState([]),[j,C]=a.useState(!1),{dispatch:d}=a.useContext(P),{dispatch:o}=a.useContext(v),i=new URLSearchParams(window.location.search),s=D(),{register:m,handleSubmit:A,formState:{errors:r}}=B({resolver:M(w)});a.useEffect(()=>{E()},[]),a.useEffect(()=>{(async()=>{const y=new URLSearchParams(window.location.search).get("stripe_return"),k=localStorage.getItem("token");if(y==="true"&&k)try{const p=new f,l={type:{value:"account"},is_default:{value:!0}};await p.AddPaymentMethod(l),c(o,"Stripe account connected successfully! Payment method set as default.",5e3,"success");const N=i.get("community");a.startTransition(()=>{s(N?`/member/communities?join_id=${N}`:"/member/dashboard")})}catch(p){console.error("Failed to set default payment method:",p),c(o,"Stripe connected but failed to set default payment method. Please try again.",5e3,"error")}})()},[s,o,i]);const E=async()=>{try{const n=await new f().callRawAPI("/v1/api/dealmaker/industries",{},"GET");!n.error&&n.data?b(n.data):c(o,n.message||"Failed to load industries",5e3,"error")}catch(t){c(o,t.message||"Failed to load industries",5e3,"error")}},_=async t=>{console.log("Form data:",t);try{g(!0);const n=new f,y=i.get("ref"),k=i.get("community"),p={...t,user_type:["both"],referral_code:y,community_id:k},l=await n.register(p);l.error||(l.token?(localStorage.setItem("token",l.token),localStorage.setItem("role","member"),d({type:"LOGIN",payload:{user:l.user_id,token:l.token,role:"member"}}),c(o,"Registration successful! Now let's connect your payment account.",5e3,"success"),C(!0)):s("/member/login",{state:{message:"Registration successful! Please login to continue."}}))}catch(n){console.error("Registration error:",n),c(o,n.message||"Registration failed. Please try again.",5e3,"error")}finally{g(!1)}},S=()=>{const t=i.get("community");a.startTransition(()=>{s(t?`/member/communities?join_id=${t}`:"/member/dashboard")})},L=()=>{const t=i.get("community");a.startTransition(()=>{s(t?`/member/communities?join_id=${t}`:"/member/dashboard")})};return e.jsxs("div",{className:"w-full min-h-screen bg-[#1E1E1E] flex flex-col",children:[e.jsxs("header",{style:{marginBottom:"20px"},className:"flex justify-between px-[5vw]  bg-[#161616] h-[62px] items-center py-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(F,{}),e.jsx("span",{className:"text-[16px] font-bold text-[#EAEAEA]",children:"RainmakerOS"})]}),e.jsxs("div",{className:"flex items-center gap-[2rem]",children:[e.jsx("a",{href:"/member/login",className:"text-[16px] text-[#eaeaea] hover:text-[#7dd87d]",children:"Home"}),e.jsx("a",{href:"/member/signup",className:"text-[16px] text-[#eaeaea] hover:text-[#7dd87d]",children:"Sign Up"})]})]}),e.jsx("div",{className:"flex-1 bg-[#1E1E1E] flex flex-col items-center w-full",children:e.jsxs("div",{style:{width:"896px"},className:"space-y-4 ",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsx("h2",{className:"text-[36px] font-bold text-[#EAEAEA]",children:"Join RainmakerOS"}),e.jsx("p",{className:"mt-1 text-[18px] text-[#B5B5B5]",children:"Create your account to start connecting"})]}),e.jsxs("form",{onSubmit:A(_),className:"space-y-4 bg-black p-[2rem] mx-auto mt-[1rem]",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#EAEAEA] mb-1",children:"First Name"}),e.jsx("input",{type:"text",...m("first_name"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Enter your first name"}),r.first_name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.first_name.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Last Name"}),e.jsx("input",{type:"text",...m("last_name"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Enter your last name"}),r.last_name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.last_name.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Email"}),e.jsx("input",{type:"email",...m("email"),autoComplete:"off",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Enter your email"}),r.email&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Industry"}),e.jsxs("select",{...m("industry"),className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none appearance-none",children:[e.jsx("option",{value:"",children:"Select your industry"}),u.map(t=>e.jsx("option",{value:t.name,children:t.name},t.id))]}),r.industry&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.industry.message})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Password"}),e.jsx("input",{type:"password",...m("password"),autoComplete:"new-password",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Create password"}),r.password&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.password.message})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-[16px] text-[#eaeaea] mb-1",children:"Confirm Password"}),e.jsx("input",{type:"password",...m("confirm_password"),autoComplete:"new-password",className:"w-full h-[50px] rounded-[8px] mt-[0.5rem] border border-[#2E7D32] bg-[#161616] px-3 text-[16px] text-[#ADAEBC] placeholder:text-[#ADAEBC] focus:border-[#2e7d32] focus:outline-none caret-[#ADAEBC] [&:-webkit-autofill]:bg-[#161616] [&:-webkit-autofill]:[-webkit-text-fill-color:#ADAEBC] [&:-webkit-autofill]:[box-shadow:0_0_0_30px_#161616_inset]",placeholder:"Confirm password"}),r.confirm_password&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.confirm_password.message})]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{type:"submit",disabled:h,className:"w-[244px] h-[56px] rounded-[8px] mt-[2rem] bg-[#2e7d32] text-[16px] font-bold text-[#EAEAEA] hover:bg-[#266d2a] focus:outline-none disabled:opacity-50",children:h?"Creating Account...":"Create Account"})})]}),e.jsx("div",{className:"text-center ",children:e.jsxs("p",{className:"text-[16px] text-[#b5b5b5] mt-[1rem]",children:["Already have an account?"," ",e.jsx("a",{href:"/member/login",className:"text-[#7dd87d] hover:text-[#6bc76b]",children:"Login here"})]})})]})}),e.jsx(q,{isOpen:j,onClose:L,onSuccess:S})]})};export{ie as default};
