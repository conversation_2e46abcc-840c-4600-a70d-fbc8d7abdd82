import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r as n}from"./vendor-1c28ea83.js";import{M as u}from"./react-tooltip-ad20e3af.js";import"./@mantine/core-76ed7ee7.js";import"./@uppy/dashboard-3a4b1704.js";import"./@craftjs/core-a5d68af1.js";const M=({display:r,className:i,children:e,tooltipClasses:l,place:d="bottom",openOnClick:c=!0,zIndex:x=99999,onPopoverStateChange:o,backgroundColor:m="#fff",textColor:a="#000"})=>{const s=n.useId(),p=()=>{o&&o(!0)},f=()=>{o&&o(!1)};return t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"w-fit",children:t.jsx("button",{type:"button","data-tooltip-id":s,className:`${i}`,children:r||null})}),t.jsx(u,{id:s,openOnClick:c,style:{backgroundColor:m,color:a},className:` ${l}  !pr-5 !shadow-md`,clickable:!0,place:d,opacity:1,afterShow:p,afterHide:f,children:e})]})};export{M as default};
