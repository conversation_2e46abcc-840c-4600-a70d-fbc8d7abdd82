import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as n,d as I}from"./vendor-1c28ea83.js";import{G as U,M as d,s as r,T as B,S as P}from"./index-826b1c0e.js";import"./Modal-8a022909.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";import"./index.esm-1ac45320.js";const T=()=>e.jsx("div",{className:"flex justify-center items-center w-10 h-10 bg-white rounded-lg",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 25 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M13.979 10.5V14.5L17.979 17V8L13.979 10.5Z",fill:"#00832D"}),e.jsx("path",{d:"M4.979 8C4.979 7.46957 5.1897 6.96086 5.56478 6.58579C5.93985 6.21071 6.44857 6 6.979 6H12.979C13.5094 6 14.0181 6.21071 14.3932 6.58579C14.7683 6.96086 14.979 7.46957 14.979 8V16C14.979 16.5304 14.7683 17.0391 14.3932 17.4142C14.0181 17.7893 13.5094 18 12.979 18H6.979C6.44857 18 5.93985 17.7893 5.56478 17.4142C5.1897 17.0391 4.979 16.5304 4.979 16V8Z",fill:"#00832D"}),e.jsx("path",{d:"M20.479 6.98C20.479 6.574 20.16 6.214 19.765 6.162L6.978 6.165C6.448 6.165 5.939 6.375 5.564 6.75C5.189 7.125 4.979 7.634 4.979 8.164V15.836C4.979 16.366 5.189 16.875 5.564 17.25C5.939 17.625 6.448 17.835 6.978 17.835H12.979C13.509 17.835 14.018 17.625 14.393 17.25C14.768 16.875 14.978 16.366 14.978 15.836V10.5L20.479 16V6.98Z",fill:"#00AC47"}),e.jsx("path",{d:"M20.479 6.98V16L17.979 13.5V8.5L19.765 6.162C20.16 6.214 20.479 6.574 20.479 6.98Z",fill:"#0066DA"}),e.jsx("path",{d:"M13.979 10.5L14.978 10.5V15.836C14.978 16.366 14.768 16.875 14.393 17.25C14.018 17.625 13.509 17.835 12.979 17.835H6.978C6.448 17.835 5.939 17.625 5.564 17.25C5.189 16.875 4.979 16.366 4.979 15.836V8.164C4.979 7.634 5.189 7.125 5.564 6.75C5.939 6.375 6.448 6.165 6.978 6.165L13.979 6.165V10.5Z",fill:"#00AC47"}),e.jsx("path",{d:"M19.765 6.162L17.979 8.5V13.5L20.479 16V6.98C20.479 6.574 20.16 6.214 19.765 6.162Z",fill:"#2684FC"}),e.jsx("path",{d:"M14.978 10.5H13.979V6.165H6.978L6.979 6.165C6.449 6.165 5.939 6.375 5.564 6.75C5.189 7.125 4.979 7.634 4.979 8.164V15.836C4.979 16.366 5.189 16.875 5.564 17.25C5.939 17.625 6.448 17.835 6.978 17.835H12.979C13.509 17.835 14.018 17.625 14.393 17.25C14.768 16.875 14.978 16.366 14.978 15.836V10.5Z",fill:"#00AC47"})]})}),R=()=>e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-lg bg-[#4A8CFF] text-white",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 2499 2500",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M2499 1253C2499 1942.5 1942 2500 1250 2500C559.5 2500 0 1942.5 0 1253C0 563.5 559.5 0 1250 0C1942 0 2499 563.5 2499 1253Z",fill:"#4A8CFF"}),e.jsx("path",{d:"M534 1090V1829.5H1254.5V1939H424V1090H534Z",fill:"white"}),e.jsx("path",{d:"M1044.5 561H2075V1410.5H1964.5V670.5H1044.5V561Z",fill:"white"}),e.jsx("path",{d:"M753 895.5H1746.5V1595H753V895.5Z",fill:"white"})]})}),O=()=>e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-lg bg-[#363636] text-white",children:e.jsx("span",{className:"font-bold text-md",children:"H"})}),K=()=>e.jsx("div",{className:"flex justify-center items-center w-10 h-10 bg-white rounded-lg",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M17.707 7.707H13.707V4.293C13.707 3.58 13.127 3 12.414 3H4.293C3.58 3 3 3.58 3 4.293V12.414C3 13.127 3.58 13.707 4.293 13.707H7.707V16.414C7.707 17.127 8.287 17.707 9 17.707H17.121C17.834 17.707 18.414 17.127 18.414 16.414V8.414C18.414 8.066 18.269 7.731 18.012 7.475C17.756 7.218 17.421 7.073 17.073 7.073L17.707 7.707Z",fill:"#5059C9"}),e.jsx("path",{d:"M17.707 7.707H13.707V4.293C13.707 3.58 13.127 3 12.414 3H4.293C3.58 3 3 3.58 3 4.293V12.414C3 13.127 3.58 13.707 4.293 13.707H7.707V16.414C7.707 17.127 8.287 17.707 9 17.707H17.121C17.834 17.707 18.414 17.127 18.414 16.414V8.414C18.414 8.066 18.269 7.731 18.012 7.475C17.756 7.218 17.421 7.073 17.073 7.073L17.707 7.707Z",fill:"url(#paint0_linear)"}),e.jsx("path",{d:"M3 12.415V10.708H13.708V17.708H9C9 17.708 7.708 17.708 7.708 16.415C7.708 15.122 7.708 13.829 7.708 13.829C7.708 13.829 7.708 13.708 7.586 13.708H4.293C4.293 13.708 3 13.708 3 12.415Z",fill:"#7B83EB"}),e.jsx("path",{opacity:"0.1",d:"M5.585 6H13.707V8.293H5.585V6Z",fill:"black"}),e.jsx("path",{opacity:"0.2",d:"M5 6.585H13.707V8.293H5V6.585Z",fill:"black"}),e.jsx("path",{opacity:"0.2",d:"M5 6.585H13.122V7.707H5V6.585Z",fill:"black"}),e.jsx("path",{opacity:"0.2",d:"M5 6.585H12.537V7.122H5V6.585Z",fill:"black"}),e.jsx("path",{d:"M12.415 3H4.293C3.58 3 3 3.58 3 4.293V12.415C3 13.128 3.58 13.708 4.293 13.708H12.415C13.128 13.708 13.708 13.128 13.708 12.415V4.293C13.708 3.58 13.128 3 12.415 3Z",fill:"url(#paint1_linear)"}),e.jsx("path",{d:"M10.122 6C10.9801 6 11.674 6.69396 11.674 7.552C11.674 8.41004 10.9801 9.104 10.122 9.104C9.26398 9.104 8.57002 8.41004 8.57002 7.552C8.57002 6.69396 9.26398 6 10.122 6Z",fill:"white"}),e.jsx("path",{d:"M12.171 11.4118H8.07304C8.07304 10.3304 8.94767 9.45581 10.0291 9.45581H10.2153C11.2967 9.45581 12.1713 10.3304 12.1713 11.4118H12.171Z",fill:"white"}),e.jsx("path",{d:"M17.122 8.293H14.293V11.122H17.122V8.293Z",fill:"#5059C9"}),e.jsx("path",{d:"M14.293 11.121H17.122V12.414C17.122 13.127 16.542 13.707 15.829 13.707H14.293V11.121Z",fill:"#5059C9"}),e.jsxs("defs",{children:[e.jsxs("linearGradient",{id:"paint0_linear",x1:"10.707",y1:"7.707",x2:"10.707",y2:"17.707",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#5A62C3"}),e.jsx("stop",{offset:"1",stopColor:"#4D55BD"})]}),e.jsxs("linearGradient",{id:"paint1_linear",x1:"8.354",y1:"3",x2:"8.354",y2:"13.708",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#5A62C3"}),e.jsx("stop",{offset:"1",stopColor:"#4D55BD"})]})]})]})}),Y=()=>e.jsx("div",{className:"flex justify-center items-center w-10 h-10 bg-white rounded-lg",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M5 5H19V19H5V5Z",fill:"white"}),e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19.778 4H4.222C3 4 2 5 2 6.222V19.778C2 21 3 22 4.222 22H19.778C21 22 22 21 22 19.778V6.222C22 5 21 4 19.778 4ZM19.778 19.778H4.222V9.611H19.778V19.778ZM4.222 6.222V7.333H19.778V6.222H4.222Z",fill:"#4285F4"}),e.jsx("path",{d:"M12.4444 14.2222H15.6667V17.4444H12.4444V14.2222Z",fill:"#4285F4"})]})}),W=()=>e.jsx("div",{className:"flex justify-center items-center w-10 h-10 bg-white rounded-lg",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M13.2 12L24 16.8V7.2L13.2 12Z",fill:"#0078D4"}),e.jsx("path",{d:"M12 11.4L0 6V18L12 12.6V11.4Z",fill:"#0078D4"}),e.jsx("path",{d:"M0 6L12 12.6L24 7.2V6C24 4.3 22.7 3 21 3H3C1.3 3 0 4.3 0 6Z",fill:"#50D9FF"}),e.jsx("path",{d:"M0 18C0 19.7 1.3 21 3 21H21C22.7 21 24 19.7 24 18V16.8L12 11.4L0 18Z",fill:"#103A9E"})]})}),be=()=>{const[p,u]=n.useState(""),[f,C]=n.useState(!0),[b,k]=n.useState([]);n.useState(!1);const[L,N]=n.useState(!1),[M,_]=n.useState(!1),[Z,m]=n.useState(!1),[c,j]=n.useState({calendar_id:"",timezone:"America/New_York",api_key:""}),g=I(),{dispatch:i}=n.useContext(U);new d,n.useEffect(()=>{let t=!0;return(async()=>{if(L||M)return;const a=new d,o=a.ParseIntegrationCallback();if(!(!o.type||!o.code))try{N(!0),_(!0);let l;if(o.type==="zoom"?l=await a.ZoomCallback(o):o.type==="google"?l=await a.GoogleCallback(o):o.type==="outlook"&&(l=await a.callRawAPI("/v1/api/dealmaker/integrations/outlook/callback?code="+o.code+"&state="+o.state,{},"GET")),!t)return;l.error?(g("/member/integrations?error="+l.message),r(i,l.message,5e3,"error")):g("/member/integrations")}catch(l){if(!t)return;console.error("Callback error:",l),g("/member/integrations?error="+(l.message||"Unknown error")),r(i,l.message||"Unknown error",5e3,"error")}})(),()=>{t=!1}},[g,i]);const w={calendar_services:[{id:"google_meet",name:"Google Calendar",description:"Sync your Google Calendar events",icon:Y,available:!0},{id:"outlook",name:"Outlook Calendar",description:"Sync your Outlook Calendar events",icon:W,available:!0},{id:"ghl_calendar",name:"HighLevel",description:"Sync your HighLevel calendar",icon:O,available:!0}],meeting_platforms:[{id:"google_meet",name:"Google Meet",description:"Connect with Google Meet",icon:T,available:!0},{id:"zoom",name:"Zoom",description:"Connect with Zoom",icon:R,available:!0},{id:"outlook",name:"Microsoft Teams",description:"Connect with Microsoft Teams",icon:K,available:!0}]},[v,H]=n.useState([]),[S,G]=n.useState([]);n.useEffect(()=>{h()},[]),n.useEffect(()=>{F()},[b]);const h=async()=>{try{C(!0);const s=await new d().GetUserIntegrations();s.error||k(s.data||[])}catch(t){u(t.message||"Failed to load integrations"),r(i,"error",t.message||"Failed to load integrations")}finally{C(!1)}},F=()=>{const t=o=>{const l=b.find(E=>E.service_id===o.id);return{id:{value:o.id},name:{value:o.name},description:{value:o.description},status:{value:l?"connected":o.available?"available":"unavailable"},integration_id:l==null?void 0:l.id,icon:o.icon,action:l?"Connected":o.available?"Connect":"Not Available"}},s=w.calendar_services.map(t);console.log(s),H(s);const a=w.meeting_platforms.map(t);console.log(a),G(a)},A=async t=>{console.log(t);try{const s=new d;if(t==="google_calendar"||t==="google_meet"){const a=await s.GetGoogleAuthUrl();!a.error&&a.auth_url&&(console.log(a.auth_url),window.location.href=a.auth_url)}else if(t==="zoom"){console.log("zoom");const a=await s.GetZoomAuthUrl();!a.error&&a.auth_url&&(console.log(a.auth_url,"the auth url"),window.location.href=a.auth_url)}else if(t==="outlook"){const a=await s.callRawAPI("/v1/api/dealmaker/user/integrations/outlook/auth-url",{},"GET");!a.error&&a.auth_url&&(window.location.href=a.auth_url)}else t==="ghl_calendar"&&m(!0)}catch(s){u(s.message||"Failed to connect service"),r(i,"error",s.message||"Failed to connect service")}},x=(t,s)=>{j(a=>({...a,[t]:s}))},D=async()=>{try{const s=await new d().ConnectGHLCalendar({calendar_id:c.calendar_id,timezone:c.timezone,api_key:c.api_key});if(!s.error){r(i,"success","GHL Calendar connected successfully"),m(!1),j({calendar_id:"",timezone:"America/New_York",api_key:""});const a=v.map(o=>{var l;return o.id.value==="ghl_calendar"?{...o,status:{value:"connected"},action:"Connected",integration_id:(l=s.data)==null?void 0:l.id}:o});H(a),await h()}}catch(t){u(t.message||"Failed to connect GHL calendar"),r(i,"error",t.message||"Failed to connect GHL calendar")}},z=async t=>{try{await new d().DisconnectIntegration(t),await h(),r(i,"success","Integration disconnected successfully")}catch(s){u(s.message||"Failed to disconnect service"),r(i,"error",s.message||"Failed to disconnect service")}},y=({service:t})=>e.jsxs("div",{className:"rounded-lg bg-[#242424] p-6",children:[e.jsxs("div",{className:"flex gap-3 items-center mb-4",children:[e.jsx(t.icon,{}),e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:t.name.value})]}),e.jsx("p",{className:"mb-6 text-sm text-[#b5b5b5]",children:t.description.value}),e.jsx("button",{onClick:()=>t.status.value==="connected"?z(t.integration_id):A(t.id.value),disabled:t.status.value==="unavailable",className:`w-full rounded-lg px-4 py-2 text-sm ${t.status.value==="connected"?"bg-[#2e7d32]/20 text-[#7dd87d]":t.status.value==="unavailable"?"cursor-not-allowed bg-[#363636] text-[#b5b5b5]":"bg-[#2e7d32] text-[#eaeaea] hover:bg-[#2e7d32]/90"}`,children:t.action})]}),V=()=>e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3",children:[...Array(3)].map((t,s)=>e.jsx(P,{className:"w-full h-48 rounded-lg"},s))});return e.jsxs("div",{className:"space-y-6",children:[p&&e.jsx(B,{message:p,type:"error"}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-bold text-[#eaeaea]",children:"Integrations"}),e.jsx("p",{className:"text-[#b5b5b5]",children:"Connect your calendars and platforms to manage meetings effortlessly"})]}),Z&&e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center p-4 backdrop-blur-sm",children:[e.jsx("div",{className:"fixed inset-0 bg-black/30",onClick:()=>m(!1)}),e.jsxs("div",{className:"relative w-full max-w-md rounded-xl bg-[#242424] p-6 shadow-2xl ring-1 ring-white/10",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:"Connect GHL Calendar"}),e.jsx("button",{onClick:()=>m(!1),className:"rounded-lg p-1 text-[#b5b5b5] hover:bg-white/5 hover:text-[#eaeaea]",children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[#b5b5b5]",children:"Calendar ID"}),e.jsx("input",{type:"text",value:c.calendar_id,onChange:t=>x("calendar_id",t.target.value),className:"w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2.5 text-[#eaeaea] placeholder-[#666] transition-colors focus:border-[#2e7d32] focus:outline-none focus:ring-1 focus:ring-[#2e7d32]",placeholder:"Enter your GHL Calendar ID"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[#b5b5b5]",children:"API Key"}),e.jsx("input",{type:"password",value:c.api_key,onChange:t=>x("api_key",t.target.value),className:"w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2.5 text-[#eaeaea] placeholder-[#666] transition-colors focus:border-[#2e7d32] focus:outline-none focus:ring-1 focus:ring-[#2e7d32]",placeholder:"Enter your GHL API Key"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[#b5b5b5]",children:"Timezone"}),e.jsx("input",{type:"text",value:c.timezone,onChange:t=>x("timezone",t.target.value),className:"w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2.5 text-[#eaeaea] placeholder-[#666] transition-colors focus:border-[#2e7d32] focus:outline-none focus:ring-1 focus:ring-[#2e7d32]",placeholder:"e.g. America/New_York"})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t border-[#363636] pt-6",children:[e.jsx("button",{onClick:()=>m(!1),className:"px-4 py-2 text-sm font-medium text-[#b5b5b5] transition-colors hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:D,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm font-medium text-[#eaeaea] transition-colors hover:bg-[#2e7d32]/90 focus:outline-none focus:ring-2 focus:ring-[#2e7d32] focus:ring-offset-2 focus:ring-offset-[#242424]",children:"Connect"})]})]})]})]}),e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Calendar Services"}),f?V():e.jsx("div",{className:"grid grid-cols-3 gap-4 md:grid-cols-2 lg:grid-cols-3",children:v.map(t=>e.jsx(y,{service:t},t.id.value))})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Meeting Platforms"}),f?V():e.jsx("div",{className:"grid grid-cols-3 gap-4 md:grid-cols-2 lg:grid-cols-3",children:S.map(t=>e.jsx(y,{service:t},t.id.value))})]})]})};export{be as default};
