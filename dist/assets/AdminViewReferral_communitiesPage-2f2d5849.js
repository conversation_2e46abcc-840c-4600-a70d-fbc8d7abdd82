import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as i,f as n}from"./vendor-1c28ea83.js";import{M as o,A as x,G as f,t as h,S as p}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";let t=new o;const K=()=>{const{dispatch:l}=i.useContext(x);i.useContext(f);const[e,c]=i.useState({}),[d,a]=i.useState(!0),m=n();return i.useEffect(function(){(async function(){try{a(!0),t.setTable("referral_communities");const r=await t.callRestAPI({id:Number(m==null?void 0:m.id),join:""},"GET");r.error||(c(r.model),a(!1))}catch(r){a(!1),console.log("error",r),h(l,r.message)}})()},[]),s.jsx("div",{className:"shadow-md rounded mx-auto p-5",children:d?s.jsx(p,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Referral_communities"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Referral_id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.referral_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Community_id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.community_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Is_primary"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.is_primary})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Created_at"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.created_at})]})})]})})};export{K as default};
