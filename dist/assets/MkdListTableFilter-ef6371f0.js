import{j as r}from"./@react-google-maps/api-211df1ae.js";import{r as y,R as M}from"./vendor-1c28ea83.js";import{a as V,d as E,M as P}from"./index-f4849d02.js";import{b as w,L as x,ap as R,aq as A}from"./index-826b1c0e.js";import B from"./SetColumns-6f4ecefa.js";import"./index-23518dfb.js";import{M as Z}from"./index-0cdf3a8c.js";import{_ as S}from"./qr-scanner-cf010ec4.js";import{G as q}from"./react-icons-5238c8a8.js";import{A as k}from"./index-e2604cb4.js";import{I as $}from"./index-632d14e3.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";import"./MkdInput-ef434b7d.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */const G=y.lazy(()=>S(()=>import("./MkdDebounceInput-ff590b47.js"),["assets/MkdDebounceInput-ff590b47.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"]));function J(d){return q({tag:"svg",attr:{viewBox:"0 0 24 24"},child:[{tag:"path",attr:{d:"M4 8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8ZM6 10V20H18V10H6ZM9 12H11V18H9V12ZM13 12H15V18H13V12ZM7 5V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V5H22V7H2V5H7ZM9 4V5H15V4H9Z"}}]})(d)}const U=({selectedOptions:d=[],columns:l=[],onColumnClick:f,setShowFilterOptions:t})=>r.jsx("div",{className:"absolute top-[-2000%] z-10 m-auto h-[31.25rem] max-h-[31.25rem] min-h-[31.25rem] w-[12.5rem] min-w-[12.5rem] max-w-[12.5rem] overflow-y-auto bg-white p-2 text-gray-600 opacity-0 shadow-md transition-all peer-focus:top-[80%] peer-focus:opacity-100 focus-within:top-[80%] focus-within:opacity-100",children:l.map(e=>{if(e!=null&&e.hasOwnProperty("isFilter")&&e.isFilter&&e.hasOwnProperty("selected_column")&&(e!=null&&e.selected_column))return r.jsx("button",{type:"button",className:"h-[2.25rem] w-full cursor-pointer text-left font-inter text-[.875rem] font-[400] capitalize leading-[1.25rem] tracking-[-0.006em] text-black",onClick:()=>{e.join?f(e==null?void 0:e.header):f(e==null?void 0:e.accessor)},children:w(e==null?void 0:e.header,{casetype:"capitalize",separator:""})},e==null?void 0:e.header);if(!(e!=null&&e.hasOwnProperty("isFilter"))&&(e!=null&&e.hasOwnProperty("selected_column"))&&!["row","action","photo","image","file","note","files","photos","images","image","thumbnial","thumbnails"].includes(e==null?void 0:e.header.toLowerCase())&&e!=null&&e.selected_column)return r.jsx("button",{type:"button",className:" h-[2.25rem] w-full cursor-pointer text-left font-inter text-[.875rem] font-[400] capitalize leading-[1.25rem] tracking-[-0.006em] text-black",onClick:()=>{e.join?f((e==null?void 0:e.filter_field)||(e==null?void 0:e.header)):f(e==null?void 0:e.accessor)},children:w(e.header,{casetype:"capitalize",separator:""})},e.header)}).filter(Boolean)}),z=y.lazy(()=>S(()=>import("./SearchableDropdown-22851455.js"),["assets/SearchableDropdown-22851455.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/react-toggle-58b0879a.js","assets/@uppy/dashboard-3a4b1704.js","assets/uuid-11097a38.js","assets/Container-2ad5434d.css","assets/MkdInput-3e37c863.css"])),W=({columnData:d=null,option:l=null,setOptionValue:f=null})=>r.jsxs(r.Fragment,{children:[["user"].includes(d==null?void 0:d.join)?r.jsx(x,{children:r.jsx(z,{table:"user",className:"flex w-full flex-col items-start ",uniqueKey:"id",displaySeparator:"-",label:w(d==null?void 0:d.accessor,{casetype:"capitalize",separator:" "}),display:[d==null?void 0:d.accessor],placeholder:d==null?void 0:d.accessor,filter:["role,cs,user","is_company,eq,1"],onSelect:(t,e)=>{e?f("value","",l==null?void 0:l.uid):f("value",t==null?void 0:t.id,l==null?void 0:l.uid)},value:l==null?void 0:l.value})}):null,r.jsx(x,{children:r.jsx(z,{className:"flex w-full flex-col items-start ",uniqueKey:"id",displaySeparator:"-",table:d==null?void 0:d.join,label:`${d==null?void 0:d.join} (${w(d==null?void 0:d.accessor,{casetype:"capitalize",separator:" "})})`,display:[d==null?void 0:d.accessor],placeholder:d==null?void 0:d.accessor,onSelect:(t,e)=>{e?f("value","",l==null?void 0:l.uid):f("value",t==null?void 0:t.id,l==null?void 0:l.uid)},value:l==null?void 0:l.value})})]}),K=y.memo(W),Q=({onSubmit:d,columns:l=[],selectedOptions:f=[],onColumnClick:t=null,setOptionValue:e=null,setSelectedOptions:v=null,onOptionValueChange:L=null,onClose:F})=>{const[N,b]=y.useState(!1);return r.jsxs("div",{className:"filter-form-holder  z-[9999999] grid h-full max-h-full min-h-full w-full min-w-full max-w-full grid-cols-1 grid-rows-[auto_1fr_auto_auto] overflow-hidden rounded-md bg-white p-5 shadow-xl",children:[r.jsxs("div",{className:"relative flex items-center justify-end",children:[r.jsx(k,{type:"button",onClick:()=>b(s=>!s),className:"!shadow-0 peer !h-fit !max-h-fit !min-h-fit w-fit !border-0  !bg-white !p-0 !py-0 font-[700] !text-primary !underline",children:"Add Filter"}),r.jsx(x,{children:r.jsx(U,{onColumnClick:s=>t&&t(s),setShowFilterOptions:b,columns:l,selectedOptions:f})})]}),r.jsx("div",{className:"overflow-y-auto",children:r.jsx("div",{className:"!h-full !max-h-full !min-h-full w-full overflow-y-auto",children:f==null?void 0:f.map((s,g)=>r.jsxs("div",{className:"mb-2 grid w-full grid-cols-[1fr_auto]  justify-between gap-2 text-gray-600",children:[l!=null&&l.length?r.jsx(r.Fragment,{children:l.map((i,a)=>i!=null&&i.selected_column&&(i==null?void 0:i.accessor)===(s==null?void 0:s.accessor)||(i==null?void 0:i.header)===(s==null?void 0:s.accessor)||(i==null?void 0:i.filter_field)===(s==null?void 0:s.accessor)?i!=null&&i.mappingExist?r.jsx(r.Fragment,{children:r.jsxs("div",{className:"grid w-full grid-cols-1 items-start justify-start",children:[r.jsx("label",{className:"mb-2 block cursor-pointer text-left text-sm font-bold text-gray-700",htmlFor:s==null?void 0:s.uid,children:w(i==null?void 0:i.accessor,{casetype:"capitalize",separator:"space"})}),r.jsxs("select",{className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem] appearance-none rounded-md border !border-gray-200 outline-0 focus:border-primary focus:ring-primary",onChange:h=>{e&&e("value",h.target.value,s==null?void 0:s.uid)},value:s==null?void 0:s.value,children:[r.jsx("option",{value:"",selected:!(s!=null&&s.value)}),Object.keys(i==null?void 0:i.mappings).map((h,H)=>r.jsx("option",{value:h,selected:h===(s==null?void 0:s.value),children:i==null?void 0:i.mappings[h]},H))]})]})}):i!=null&&i.join?r.jsx("div",{className:"flex w-full items-end justify-start",children:r.jsx(K,{columnData:i,option:s,setOptionValue:e})},a):r.jsx("div",{className:"flex w-full items-end justify-start  !px-[.0625rem]",children:r.jsx(x,{children:r.jsx(G,{type:"text",placeholder:"Enter value...",label:w(i==null?void 0:i.accessor,{casetype:"capitalize",separator:" "}),setValue:h=>{e&&e("value",h,s==null?void 0:s.uid)},value:s==null?void 0:s.value,showIcon:!1,className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem] !w-full !min-w-full !max-w-full !rounded-md !border !border-gray-200 !px-3 !py-2 !leading-tight !text-gray-700 !outline-none focus:border-primary focus:ring-primary",onReady:h=>{}})})},a):null)}):null,r.jsx(J,{className:"cursor-pointer self-end text-2xl !text-sub-500",onClick:()=>{v(i=>i.filter(a=>a.uid!==(s==null?void 0:s.uid)))}})]},g))})}),r.jsxs("div",{className:"mt-5  flex w-full  gap-5",children:[r.jsx(k,{type:"button",onClick:()=>F(),className:"!grow self-end !border-gray-200 !bg-transparent font-bold !text-primary",children:"Cancel"}),r.jsx($,{type:"button",onClick:()=>{d&&(F(),d())},className:"!grow self-end rounded px-4 py-2 font-bold capitalize text-white",children:"Apply and Close"})]}),r.jsx("div",{className:"flex items-center justify-center",children:r.jsx(k,{type:"button",onClick:()=>v(()=>[]),disabled:(f==null?void 0:f.length)===0,className:"!shadow-0 w-fit !border-0 !bg-white font-[700] !text-sub-500 !underline",children:"Clear all Filters"})})]})},X=y.memo(Q),ze=({columns:d,table:l="",onSubmit:f,columnData:t,columnId:e=0,setColumns:v,setColumnId:L,searchField:F,setColumnData:N,onColumnClick:b,setOptionValue:s,selectedOptions:g,columnModel:i="",setSelectedOptions:a,filterDisplays:h=[],setFilterConditions:H,onOptionValueChange:c})=>{const[I,p]=M.useState(!1),[T,_]=M.useState(!1);return M.useState(!1),r.jsx(r.Fragment,{children:r.jsxs("div",{className:"relative flex hidden w-fit items-center justify-between rounded bg-white",children:[r.jsx("div",{className:"flex w-full flex-col items-start justify-between gap-4 text-gray-700 md:flex-row md:items-center",children:r.jsx(x,{children:r.jsx(V,{columns:t==null?void 0:t.columns,columnData:t,selectedOptions:g,display:[E.FILTER,...h],setOpenColumns:()=>_(!0),setOpenFilter:()=>p(j=>!j)})})}),r.jsx(x,{children:r.jsx(Z,{isModalActive:I,closeModalFn:()=>p(!1),customMinWidthInTw:"md:!w-[25%] !w-full ",showHeader:!0,title:r.jsxs("div",{className:"flex items-center gap-2 font-inter text-[1.125rem] font-bold leading-[1.5rem] text-[#18181B]",children:[r.jsx(R,{})," Filter"]}),side:"left",headerClassName:"bg-white text-black",headerContentClassName:"text-black",closePosition:2,headerContent:r.jsx(P,{onToggleModal:()=>p(!1),cancelText:r.jsx(A,{className:"!h-[.755rem] !w-[.755rem]"})}),classes:{modalBody:"bg-white"},children:r.jsx(x,{children:r.jsx(X,{onSubmit:f,columns:t==null?void 0:t.columns,onColumnClick:b,setOptionValue:s,selectedOptions:g,setSelectedOptions:a,onOptionValueChange:c,onClose:()=>p(!1)})})})}),r.jsx(x,{children:r.jsx(x,{children:r.jsx(B,{isOpen:T,columnModel:i,columns:t==null?void 0:t.columns,onClose:()=>_(!1),columnData:t,onUpdate:j=>{N(C=>({...C,...j}))},onSuccess:j=>{_(!1),N(C=>({...C,...j}))}})})})]})})};export{ze as default};
