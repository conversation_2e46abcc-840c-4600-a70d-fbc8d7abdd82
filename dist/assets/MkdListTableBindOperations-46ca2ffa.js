const p={EQUAL:"eq",NOT_EQUAL:"neq",IS_NULL:"isn",IS_NOT_NULL:"isnn",CONTAINS:"cs",START_WITH:"sw",END_WITH:"ew",LESS_THAN:"lt",GREATER_THAN:"gt"},H=(r,e,s)=>{var u;return Array.isArray(r[e])?((u=r[e])==null?void 0:u.length)==s:r[e]==s},U=(r,e,s)=>{var u;return Array.isArray(r[e])?((u=r[e])==null?void 0:u.length)!=s:r[e]!=s},W=(r,e,s)=>r[e].includes(s),R=(r,e,s)=>r[e]==null||r[e]==null,V=(r,e,s)=>r[e]!=null||r[e]!=null,q=(r,e,s)=>r[e].startsWith(s),F=(r,e,s)=>r[e].endsWith(s),Q=(r,e,s)=>{var u;if(Array.isArray(r[e]))return((u=r[e])==null?void 0:u.length)>s;if(["number"].includes(typeof r[e]))return r[e]>s;if(["string"].includes(typeof r[e]))return isNaN(Number(r[e]))?!1:r[e]>s},C=(r,e,s)=>{var u;if(Array.isArray(r[e]))return((u=r[e])==null?void 0:u.length)<s;if(["number"].includes(typeof r[e]))return r[e]<s;if(["string"].includes(typeof r[e]))return isNaN(Number(r[e]))?!1:r[e]<s},I=(r,e,s,u)=>{switch(s){case p.EQUAL:return H(r,e,u);case p.NOT_EQUAL:return U(r,e,u);case p.IS_NULL:return R(r,e);case p.IS_NOT_NULL:return V(r,e);case p.CONTAINS:return W(r,e,u);case p.START_WITH:return q(r,e,u);case p.END_WITH:return F(r,e,u);case p.GREATER_THAN:return Q(r,e,u);case p.LESS_THAN:return C(r,e,u);default:return!1}},D=(r,e)=>{var u,l,d,f,N,T,g;return!Array.isArray((u=r==null?void 0:r.bind)==null?void 0:u.column)||!Array.isArray((l=r==null?void 0:r.bind)==null?void 0:l.ifValue)||((f=(d=r==null?void 0:r.bind)==null?void 0:d.column)==null?void 0:f.length)!==((T=(N=r==null?void 0:r.bind)==null?void 0:N.ifValue)==null?void 0:T.length)?!1:((g=r==null?void 0:r.bind)==null?void 0:g.column.map((A,b)=>{var O,h,_,E,L,S;let y=null;return["string"].includes(typeof((O=r==null?void 0:r.bind)==null?void 0:O.operator))?y=(h=r==null?void 0:r.bind)==null?void 0:h.operator:Array.isArray((_=r==null?void 0:r.bind)==null?void 0:_.operator)&&(E=r==null?void 0:r.bind)!=null&&E.operator[b]&&(y=(L=r==null?void 0:r.bind)==null?void 0:L.operator[b]),I(e,A,y,(S=r==null?void 0:r.bind)==null?void 0:S.ifValue[b])})).some(A=>A===!0)},G=(r,e)=>{var u,l,d,f,N,T,g;return!Array.isArray((u=r==null?void 0:r.bind)==null?void 0:u.column)||!Array.isArray((l=r==null?void 0:r.bind)==null?void 0:l.ifValue)||((f=(d=r==null?void 0:r.bind)==null?void 0:d.column)==null?void 0:f.length)!==((T=(N=r==null?void 0:r.bind)==null?void 0:N.ifValue)==null?void 0:T.length)?!1:((g=r==null?void 0:r.bind)==null?void 0:g.column.map((A,b)=>{var O,h,_,E,L,S;let y=null;return["string"].includes(typeof((O=r==null?void 0:r.bind)==null?void 0:O.operator))?y=(h=r==null?void 0:r.bind)==null?void 0:h.operator:Array.isArray((_=r==null?void 0:r.bind)==null?void 0:_.operator)&&(E=r==null?void 0:r.bind)!=null&&E.operator[b]&&(y=(L=r==null?void 0:r.bind)==null?void 0:L.operator[b]),I(e,A,y,(S=r==null?void 0:r.bind)==null?void 0:S.ifValue[b])})).every(A=>A===!0)},B=(r,e)=>{var s,u,l,d,f;if((s=r==null?void 0:r.bind)!=null&&s.logic)switch((u=r==null?void 0:r.bind)==null?void 0:u.logic){case"or":return D(r,e);case"and":return G(r,e)}else return I(e,(l=r==null?void 0:r.bind)==null?void 0:l.column,(d=r==null?void 0:r.bind)==null?void 0:d.operator,(f=r==null?void 0:r.bind)==null?void 0:f.ifValue)},M=(r,e)=>{const s=e==null?void 0:e.map(f=>B(r,f));return(s==null?void 0:s.every(f=>f===!1))?!1:!!((s==null?void 0:s.every(f=>f===!0))||(s==null?void 0:s.some(f=>f===!1)))};export{B as a,M as p};
