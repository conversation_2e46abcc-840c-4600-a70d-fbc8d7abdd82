import{j as s}from"./@react-google-maps/api-211df1ae.js";import{R as l,f as x}from"./vendor-1c28ea83.js";import{M as n,A as h,G as j,t as o,S as f}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";let c=new n;const F=()=>{const{dispatch:r}=l.useContext(h);l.useContext(j);const[e,t]=l.useState({}),[d,i]=l.useState(!0),m=x();return l.useEffect(function(){(async function(){try{i(!0),c.setTable("tokens");const a=await c.callRestAPI({id:Number(m==null?void 0:m.id),join:""},"GET");a.error||(t(a.model),i(!1))}catch(a){i(!1),console.log("error",a),o(r,a.message)}})()},[]),s.jsx("div",{className:"shadow-md rounded mx-auto p-5",children:d?s.jsx(f,{}):s.jsxs(s.Fragment,{children:[s.jsx("h4",{className:"text-2xl font-medium",children:"View Tokens"}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"User_id"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.user_id})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Token"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.token})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Code"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.code})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Type"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.type})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Data"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.data})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Status"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.status})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Created_at"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.created_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Updated_at"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.updated_at})]})}),s.jsx("div",{className:"mb-4 mt-4",children:s.jsxs("div",{className:"flex mb-4",children:[s.jsx("div",{className:"flex-1",children:"Expired_at"}),s.jsx("div",{className:"flex-1",children:e==null?void 0:e.expired_at})]})})]})})};export{F as default};
