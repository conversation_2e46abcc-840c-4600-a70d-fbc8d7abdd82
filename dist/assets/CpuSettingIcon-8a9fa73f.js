import{j as C}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const M=({pathFill:V="white",stroke:o="",fill:t="none",className:H="",fillOpacity:s="0.6",onClick:e=()=>{}})=>C.jsxs("svg",{onClick:e,className:H,width:"20",height:"20",viewBox:"0 0 20 20",fill:t,xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{d:"M3.40898 6.73301C3.36732 6.98301 3.33398 7.23301 3.33398 7.49967C3.33398 7.24134 3.36732 6.98301 3.40898 6.73301ZM3.33398 12.4997C3.33398 12.7663 3.36732 13.0163 3.40898 13.2663C3.36732 13.0163 3.33398 12.758 3.33398 12.4997ZM6.74232 16.5913C6.98398 16.633 7.24232 16.6663 7.50065 16.6663C7.24232 16.6663 6.99232 16.633 6.74232 16.5913ZM6.74232 3.40801C6.99232 3.36634 7.24232 3.33301 7.50065 3.33301C7.24232 3.33301 6.98398 3.36634 6.74232 3.40801ZM13.2673 3.40801C13.0173 3.36634 12.7673 3.33301 12.5007 3.33301C12.759 3.33301 13.0173 3.36634 13.2673 3.40801ZM16.5923 6.73301C16.634 6.98301 16.6673 7.24134 16.6673 7.49967C16.6673 7.23301 16.634 6.98301 16.5923 6.73301Z",fill:V,fillOpacity:s}),C.jsx("path",{d:"M18.3327 6.91699C18.3327 7.26699 18.0577 7.54199 17.7077 7.54199H16.666V9.07289C16.666 9.35087 16.3643 9.52396 16.1243 9.38366V9.38366C15.766 9.17533 15.3493 9.10866 14.9493 9.21699V9.21699C14.6928 9.27857 14.3827 9.11276 14.3827 8.8489V8.11699C14.3827 6.74199 13.2577 5.61699 11.8827 5.61699H8.11602C6.74102 5.61699 5.61602 6.74199 5.61602 8.11699V11.8837C5.61602 13.2587 6.74102 14.3837 8.11602 14.3837H8.49102C8.58767 14.3837 8.66602 14.462 8.66602 14.5587V14.5587C8.66602 15.3616 9.24788 16.0367 10.0139 16.1918C10.158 16.221 10.2555 16.3933 10.2077 16.5323V16.5323C10.1803 16.6118 10.1078 16.667 10.0238 16.667H7.54935V17.7087C7.54935 18.0503 7.26602 18.3337 6.92435 18.3337C6.57435 18.3337 6.29935 18.0503 6.29935 17.7087V16.4837C4.96602 16.0753 3.92435 15.0337 3.52435 13.7087H2.29102C1.94935 13.7087 1.66602 13.4253 1.66602 13.0837C1.66602 12.7337 1.94935 12.4587 2.29102 12.4587H3.33268V10.6253H2.29102C1.94935 10.6253 1.66602 10.342 1.66602 10.0003C1.66602 9.65033 1.94935 9.37533 2.29102 9.37533H3.33268V7.54199H2.29102C1.94935 7.54199 1.66602 7.26699 1.66602 6.91699C1.66602 6.57533 1.94935 6.29199 2.29102 6.29199H3.52435C3.92435 4.96699 4.96602 3.92533 6.29935 3.51699V2.29199C6.29935 1.95033 6.57435 1.66699 6.92435 1.66699C7.26602 1.66699 7.54935 1.95033 7.54935 2.29199V3.33366H9.37435V2.29199C9.37435 1.95033 9.65768 1.66699 9.99935 1.66699C10.341 1.66699 10.6243 1.95033 10.6243 2.29199V3.33366H12.4577V2.29199C12.4577 1.95033 12.741 1.66699 13.0827 1.66699C13.4243 1.66699 13.7077 1.95033 13.7077 2.29199V3.52533C15.0327 3.92533 16.0743 4.96699 16.4743 6.29199H17.7077C18.0577 6.29199 18.3327 6.57533 18.3327 6.91699Z",fill:V,fillOpacity:s}),C.jsx("path",{d:"M17.4917 12.941C16.925 12.941 16.6 12.6576 16.6 12.241C16.6 12.0743 16.65 11.8826 16.7667 11.6826C17 11.2826 16.8583 10.766 16.4583 10.541L15.7 10.0993C15.35 9.89096 14.9 10.016 14.6917 10.366L14.6417 10.4493C14.2417 11.141 13.5917 11.141 13.1917 10.4493L13.1417 10.366C12.9417 10.016 12.4833 9.89096 12.1417 10.0993L11.375 10.541C11.1083 10.691 10.95 10.9743 10.95 11.266C10.95 11.4076 10.9917 11.5493 11.0667 11.6826C11.1833 11.8826 11.2417 12.0743 11.2417 12.241C11.2417 12.6576 10.9083 12.941 10.3417 12.941C9.88333 12.941 9.5 13.316 9.5 13.7743V14.5576C9.5 15.016 9.88333 15.391 10.3417 15.391C10.9083 15.391 11.2417 15.6743 11.2417 16.091C11.2417 16.2576 11.1833 16.4493 11.0667 16.6493C10.8333 17.0493 10.975 17.566 11.375 17.791L12.1417 18.2326C12.4833 18.441 12.9417 18.316 13.1417 17.966L13.1917 17.8826C13.5917 17.191 14.2417 17.191 14.6417 17.8826L14.6917 17.966C14.9 18.316 15.35 18.441 15.7 18.2326L16.4583 17.791C16.725 17.641 16.8833 17.3576 16.8833 17.066C16.8833 16.9243 16.8417 16.7826 16.7667 16.6493C16.65 16.4493 16.6 16.2576 16.6 16.091C16.6 15.6743 16.925 15.391 17.4917 15.391C17.9583 15.391 18.3333 15.016 18.3333 14.5576V13.7743C18.3333 13.316 17.9583 12.941 17.4917 12.941ZM13.9167 15.5993C13.125 15.5993 12.4833 14.9576 12.4833 14.166C12.4833 13.3743 13.125 12.7326 13.9167 12.7326C14.7083 12.7326 15.35 13.3743 15.35 14.166C15.35 14.9576 14.7083 15.5993 13.9167 15.5993Z",fill:V,fillOpacity:s}),C.jsx("path",{d:"M13.5423 8.34967V8.82679C13.5423 9.10519 13.1713 9.28166 12.9007 9.21634V9.21634C12.484 9.10801 12.0673 9.16634 11.7257 9.37467L10.9673 9.81634C10.4423 10.108 10.1173 10.6663 10.1173 11.2663C10.1173 11.316 10.1194 11.366 10.1236 11.416C10.1509 11.7379 9.92588 12.1284 9.63342 12.2656C9.23125 12.4541 8.91555 12.7978 8.76443 13.2184C8.69974 13.3984 8.54198 13.5413 8.35065 13.5413V13.5413C7.30065 13.5413 6.45898 12.6997 6.45898 11.6497V8.34967C6.45898 7.29967 7.30065 6.45801 8.35065 6.45801H11.6507C12.7007 6.45801 13.5423 7.29967 13.5423 8.34967Z",fill:V,fillOpacity:s})]});export{M as default};
