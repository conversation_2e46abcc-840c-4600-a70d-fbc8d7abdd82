import{j as s}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{A as k}from"./index.esm-4be700bd.js";import{L as w,ao as A,au as F,E as L,av as M,d as z}from"./index-826b1c0e.js";import{M as O}from"./index-dbfe2d0c.js";import{D as u}from"./index-6bbb3077.js";import"./index-e2604cb4.js";import{o as T}from"./config-c6ac2907.js";import{a as R}from"./MkdListTableBindOperations-46ca2ffa.js";import"./react-icons-5238c8a8.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const t=({action:e,row:p,actionId:r,key:d})=>{var j;if(e!=null&&e.bind)switch((j=e==null?void 0:e.bind)==null?void 0:j.action){case"hide":if(!R(e,p))return s.jsx(w,{children:s.jsx(u,{name:(e==null?void 0:e.children)??d,className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] bg-white hover:!bg-white-100",icon:e==null?void 0:e.icon,onClick:()=>{e!=null&&e.action&&(e==null||e.action([p[r]]))}})})}if(!(e!=null&&e.bind))return s.jsx(w,{children:s.jsx(u,{name:(e==null?void 0:e.children)??d,className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] bg-white hover:!bg-white-100",icon:e==null?void 0:e.icon,onClick:()=>{e!=null&&e.action&&(e==null||e.action([p[r]]))}})})},B=({action:e,actionKey:p,row:r,actionId:d})=>s.jsx(w,{children:s.jsx(O,{display:s.jsxs("span",{className:"flex  w-full cursor-pointer items-center justify-between gap-3 px-2 capitalize text-[#262626] hover:bg-[#F4F4F4]",children:[s.jsxs("span",{className:"flex grow gap-3",children:[e==null?void 0:e.icon,(e==null?void 0:e.children)??p]}),s.jsx(A,{})]}),className:"w-full",tooltipClasses:"!rounded-[.5rem] w-full !min-w-fit !w-fit !max-w-fit !px-0 !right-[3.25rem] !border bg-white",place:"left-start",backgroundColor:"#fff",children:e!=null&&e.options&&Object.keys(e==null?void 0:e.options).length?Object.keys(e==null?void 0:e.options).map((j,b)=>s.jsx(t,{action:e==null?void 0:e.options[j],actionId:d,row:r},b)):null})}),K=(e,p)=>{var r;return e!=null&&e.bind&&["hide"].includes((r=e==null?void 0:e.bind)==null?void 0:r.action)?!R(e,p):!0},P=(e,p,r)=>p.mappingExist?p.mappingAction[r[e]]:e,xe=({row:e,columns:p,actions:r,actionId:d="id",setDeleteId:j,onPopoverStateChange:b=null})=>{var g,i,v,N;const D=p==null?void 0:p.find(m=>["status","verify","receipt_status"].includes(m==null?void 0:m.accessor)),E=Object.keys(e).find(m=>["status","verify","receipt_status"].includes(m));return s.jsx(s.Fragment,{children:Object.keys(r).filter(m=>{var l,h,f,x,C,y;return((l=r[m])==null?void 0:l.show)&&((h=r[m])==null?void 0:h.locations)&&((x=(f=r[m])==null?void 0:f.locations)==null?void 0:x.length)&&((y=(C=r[m])==null?void 0:C.locations)==null?void 0:y.includes("dropdown"))&&K(r[m],e)}).length?s.jsx("div",{className:"items center z-3 relative flex h-fit w-fit",children:s.jsx(w,{children:s.jsxs(O,{display:s.jsx(F,{className:"rotate-90"}),tooltipClasses:"!rounded-[.5rem] !min-w-fit !w-fit !max-w-fit !px-0 !right-[3.25rem] !border bg-white",place:"left-end",onPopoverStateChange:b,children:[((g=r==null?void 0:r.edit)==null?void 0:g.show)&&s.jsx(w,{children:s.jsx(u,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] bg-white",icon:s.jsx(L,{}),name:"Edit",onClick:()=>{var m,l;(m=r==null?void 0:r.edit)!=null&&m.action&&((l=r==null?void 0:r.edit)==null||l.action([e[d]]))}})}),((i=r==null?void 0:r.view)==null?void 0:i.show)&&s.jsx(w,{children:s.jsx(u,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] bg-white",icon:s.jsx(k,{className:"text-gray-400"}),name:"View",onClick:()=>{var m,l;(m=r==null?void 0:r.view)!=null&&m.action&&((l=r==null?void 0:r.view)==null||l.action([e[d]]))}})}),((v=r==null?void 0:r.status)==null?void 0:v.show)&&s.jsx(w,{children:s.jsx(u,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] bg-white",icon:s.jsx(M,{}),name:P(E,D,e),onClick:()=>{var m,l;(m=r==null?void 0:r.status)!=null&&m.action&&((l=r==null?void 0:r.status)==null||l.action([e[d]]))}})}),((N=r==null?void 0:r.delete)==null?void 0:N.show)&&s.jsx(w,{children:s.jsx(u,{className:"!w-[11rem] !min-w-[11rem] !max-w-[11rem] bg-white",icon:s.jsx(z,{}),name:"Delete",onClick:()=>{var m,l,h;(m=r[key])!=null&&m.action?(l=r[key])!=null&&l.action&&((h=r[key])==null||h.action([e[d]])):j&&j(e[d])}})}),Object.keys(r).filter(m=>{var l,h,f,x;return((l=r[m])==null?void 0:l.show)&&((h=r[m])==null?void 0:h.locations)&&((x=(f=r[m])==null?void 0:f.locations)==null?void 0:x.includes("dropdown"))}).map((m,l)=>{var h,f,x;if((h=r[m])!=null&&h.type&&[T.DROPDOWN].includes((f=r[m])==null?void 0:f.type))return s.jsx(B,{row:e,actionKey:m,actionId:d,action:r[m]},l);if(!((x=r[m])!=null&&x.type))return s.jsx(t,{row:e,actionId:d,action:r[m]},l)})]})})}):null})};export{xe as default};
