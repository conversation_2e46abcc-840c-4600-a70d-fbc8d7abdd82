import{j as e}from"./@react-google-maps/api-211df1ae.js";import{f as De,d as Ae,r as d}from"./vendor-1c28ea83.js";import{G as Le,M as x,s as o}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const T=l=>{const i=new Date(l),r=i.getDate(),a=i.toLocaleString("default",{month:"short"}),N=i.getFullYear();return`${r} ${a}, ${N}`},Re=l=>l.split(" ").map(i=>i[0]).join("").toUpperCase().slice(0,2),Fe=({user:l})=>{var i,r,a;return(i=l==null?void 0:l.photo)!=null&&i.value?e.jsx("img",{src:l.photo.value,alt:((r=l.name)==null?void 0:r.value)||"User",className:"object-cover w-8 h-8 rounded-full"}):e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:Re(((a=l==null?void 0:l.name)==null?void 0:a.value)||"Unknown")})},pe=({text:l="",maxWords:i=100})=>{const[r,a]=d.useState(!1);if(!l)return e.jsx("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:"No content available"});const N=l.split(/\s+/),E=N.length>i,S=r?l:N.slice(0,i).join(" ")+(E?"...":"");return e.jsx("div",{children:e.jsxs("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:[S,E&&e.jsx("button",{onClick:()=>a(!r),className:"ml-2 text-[#7dd87d] hover:underline",children:r?"Read Less":"Read More"})]})})},st=()=>{var Q,X,Z,ee,te,ae,se,ne,le,re,de,ie,oe,ce;const{id:l}=De(),i=Ae(),{dispatch:r}=d.useContext(Le),[a,N]=d.useState(null),[E,S]=d.useState(!0),[Y,K]=d.useState(""),[f,P]=d.useState("details"),[I,be]=d.useState([]),[M,ve]=d.useState([]),[je,$]=d.useState(!1),[ge,U]=d.useState(!1),[B,z]=d.useState(""),[y,D]=d.useState({content:"",due_date:""}),[u,G]=d.useState(null),[h,H]=d.useState(null),[A,L]=d.useState(""),[k,w]=d.useState({content:"",due_date:""}),[j,q]=d.useState({show:!1,type:null,item:null});d.useEffect(()=>{l&&(Ne(),R(),F())},[l]);const Ne=async()=>{try{S(!0);const s=await new x().GetReferralDetail(l);!s.error&&s.model?N(s.model):K(s.message||"Failed to load referral details")}catch(t){K(t.message||"Failed to load referral details")}finally{S(!1)}},R=async()=>{try{const s=await new x().callRawAPI(`/v1/api/dealmaker/user/notes?referral_id=${l}`,{},"GET");s.error||be(s.list||[])}catch(t){console.error("Failed to load notes:",t)}},F=async()=>{try{const s=await new x().callRawAPI(`/v1/api/dealmaker/user/tasks?referral_id=${l}`,{},"GET");s.error||ve(s.list||[])}catch(t){console.error("Failed to load tasks:",t)}},fe=async()=>{if(B.trim())try{const s=await new x().callRawAPI("/v1/api/dealmaker/user/notes",{content:{value:B},referral_id:{value:l}},"POST");if(!s.error)z(""),$(!1),R(),o(r,"Note added successfully!",5e3,"success");else throw new Error(s.message||"Failed to add note")}catch(t){o(r,t.message||"Failed to add note",5e3,"error")}},ke=async()=>{if(y.content.trim())try{const s=await new x().callRawAPI("/v1/api/dealmaker/user/tasks",{content:{value:y.content},due_date:{value:y.due_date},referral_id:{value:l}},"POST");if(!s.error)D({content:"",due_date:""}),U(!1),F(),o(r,"Task added successfully!",5e3,"success");else throw new Error(s.message||"Failed to add task")}catch(t){o(r,t.message||"Failed to add task",5e3,"error")}},J=(t,s)=>{q({show:!0,type:t,item:s})},O=()=>{q({show:!1,type:null,item:null})},ye=async t=>{try{const n=await new x().callRawAPI(`/v1/api/dealmaker/user/notes/${t}`,{},"DELETE");if(!n.error)R(),o(r,"Note deleted successfully!",5e3,"success"),O();else throw new Error(n.message||"Failed to delete note")}catch(s){o(r,s.message||"Failed to delete note",5e3,"error")}},we=async t=>{try{const n=await new x().callRawAPI(`/v1/api/dealmaker/user/tasks/${t}`,{},"DELETE");if(!n.error)F(),o(r,"Task deleted successfully!",5e3,"success"),O();else throw new Error(n.message||"Failed to delete task")}catch(s){o(r,s.message||"Failed to delete task",5e3,"error")}},_e=async()=>{var t;if(A.trim())try{const n=await new x().callRawAPI(`/v1/api/dealmaker/user/notes/${((t=u.id)==null?void 0:t.value)||u.id}`,{content:{value:A}},"POST");if(!n.error)G(null),L(""),R(),o(r,"Note updated successfully!",5e3,"success");else throw new Error(n.message||"Failed to update note")}catch(s){console.error("Error updating note:",s),o(r,s.message||"Failed to update note",5e3,"error")}},Ce=async()=>{var t;if(k.content.trim())try{const n=await new x().callRawAPI(`/v1/api/dealmaker/user/tasks/${((t=h.id)==null?void 0:t.value)||h.id}`,{content:{value:k.content},due_date:{value:k.due_date}},"POST");if(!n.error)H(null),w({content:"",due_date:""}),F(),o(r,"Task updated successfully!",5e3,"success");else throw new Error(n.message||"Failed to update task")}catch(s){console.error("Error updating task:",s),o(r,s.message||"Failed to update task",5e3,"error")}},Te=t=>{var s,n;G(t),L(((s=t.content)==null?void 0:s.value)||t.content||((n=t.description)==null?void 0:n.value)||t.description||"")},Ee=t=>{var m,p,b,v,g,_;H(t);const s=t.description||((p=(m=t.event_details)==null?void 0:m.value)==null?void 0:p.content)||((b=t.content)==null?void 0:b.value)||t.content||"",n=t.due_date||((g=(v=t.event_details)==null?void 0:v.value)==null?void 0:g.due_date)||((_=t.due_date)==null?void 0:_.value)||"";let c="";if(n){const C=new Date(n);isNaN(C.getTime())||(c=C.toISOString().slice(0,16))}w({content:s,due_date:c})},Se=t=>{var s,n,c,m;return!t.referral_type||!t.referral_type.value?e.jsx("div",{className:"flex gap-2 items-center",children:e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Open Referral"})}):t.referral_type.value==="community referral"?e.jsx("div",{className:"flex gap-2 items-center",children:e.jsxs("span",{className:"text-sm text-[#eaeaea]",children:["Community Referral to ",((n=(s=t.community)==null?void 0:s.title)==null?void 0:n.value)||"Unknown Community"]})}):t.referral_type.value==="direct referral"?e.jsx("div",{className:"flex gap-2 items-center",children:e.jsxs("span",{className:"text-sm text-[#eaeaea]",children:["Direct Referral to ",((m=(c=t.referred_to)==null?void 0:c.name)==null?void 0:m.value)||"Unknown Person"]})}):e.jsx("div",{className:"flex gap-2 items-center",children:e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Referral"})})};return E?e.jsx("div",{className:"flex justify-center items-center min-h-screen",children:e.jsx("div",{className:"text-[#eaeaea]",children:"Loading referral details..."})}):Y?e.jsxs("div",{className:"flex flex-col justify-center items-center min-h-screen",children:[e.jsx("div",{className:"text-red-500 mb-4",children:Y}),e.jsx("button",{onClick:()=>i(-1),className:"px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]",children:"Go Back"})]}):a?e.jsxs("div",{className:"min-h-screen bg-[#0a0a0a] p-4 md:p-6",children:[e.jsx("style",{children:`
          input[type="date"]::-webkit-calendar-picker-indicator,
          input[type="datetime-local"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover,
          input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}),e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("button",{onClick:()=>i(-1),className:"flex items-center gap-2 text-[#b5b5b5] hover:text-[#eaeaea] mb-4",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Go Back"]}),e.jsx("div",{className:"bg-[#161616] rounded-xl p-6",children:e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx(Fe,{user:a.creator}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-[#eaeaea] mb-2",children:((Q=a.title)==null?void 0:Q.value)||"Untitled Referral"}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("p",{className:"text-[#b5b5b5]",children:[((Z=(X=a.creator)==null?void 0:X.name)==null?void 0:Z.value)||"Unknown"," - ",T((ee=a.created_at)==null?void 0:ee.value)]}),Se(a)]})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("span",{className:"rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]",children:["Status: ",((te=a.status)==null?void 0:te.value)||"Unknown"]})})]})})]}),e.jsxs("div",{className:"bg-[#161616] rounded-xl",children:[e.jsx("div",{className:"border-b border-[#363636]",children:e.jsxs("div",{className:"flex",children:[e.jsx("button",{onClick:()=>P("details"),className:`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${f==="details"?"border-[#2e7d32] text-[#eaeaea]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Details"}),e.jsxs("button",{onClick:()=>P("notes"),className:`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${f==="notes"?"border-[#2e7d32] text-[#eaeaea]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:["Notes (",I.length,")"]}),e.jsxs("button",{onClick:()=>P("tasks"),className:`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${f==="tasks"?"border-[#2e7d32] text-[#eaeaea]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:["Tasks (",M.length,")"]})]})}),e.jsxs("div",{className:"p-6",children:[f==="details"&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Type"}),e.jsx("p",{className:"text-[#b5b5b5]",children:(ae=a==null?void 0:a.type)!=null&&ae.value?a.type.value==="looking_for_service"?"Looking for Service":a.type.value==="looking_for_product"?"Looking for Product":a.type.value==="looking_for_buyer"?"Looking for Buyer":a.type.value==="looking_for_investor"?"Looking for Investor":a.type.value==="looking_for_partner"?"Looking for Partner":a.type.value.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase()):"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Industry"}),e.jsx("p",{className:"text-[#b5b5b5]",children:((se=a==null?void 0:a.industry_name)==null?void 0:se.value)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Description"}),e.jsx(pe,{text:(ne=a==null?void 0:a.description)==null?void 0:ne.value,maxWords:1e3})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Deal Size"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["$",((le=a==null?void 0:a.deal_size)==null?void 0:le.value)||"N/A"]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Referral Fee"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:[((re=a==null?void 0:a.referral_fee)==null?void 0:re.value)||"N/A","%"]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Payment Method"}),e.jsx("p",{className:"text-[#b5b5b5]",children:(de=a==null?void 0:a.payment_method)!=null&&de.value?a.payment_method.value==="bank"?"Bank Transfer":a.payment_method.value==="stripe"?"Stripe":a.payment_method.value.replace(/_/g," ").replace(/\b\w/g,t=>t.toUpperCase()):"N/A"})]}),((ie=a==null?void 0:a.additional_notes)==null?void 0:ie.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Additional Notes"}),e.jsx(pe,{text:a.additional_notes.value,maxWords:500})]}),((oe=a==null?void 0:a.expiration_date)==null?void 0:oe.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Expiration Date"}),e.jsx("p",{className:"text-[#b5b5b5]",children:T(a.expiration_date.value)})]}),((ce=a==null?void 0:a.description_image)==null?void 0:ce.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Attached Image"}),e.jsx("img",{src:a.description_image.value,alt:"Description",className:"max-h-96 rounded-lg object-contain"})]})]}),f==="notes"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"font-medium text-[#eaeaea]",children:"Notes"}),e.jsx("button",{onClick:()=>$(!0),className:"px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]",children:"Add Note"})]}),je&&e.jsxs("div",{className:"bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]",children:[e.jsx("textarea",{value:B,onChange:t=>z(t.target.value),placeholder:"Enter your note...",className:"w-full h-24 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"}),e.jsxs("div",{className:"flex gap-2 mt-3",children:[e.jsx("button",{onClick:fe,className:"px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]",children:"Save Note"}),e.jsx("button",{onClick:()=>{$(!1),z("")},className:"px-4 py-2 bg-[#363636] text-[#eaeaea] rounded-lg hover:bg-[#424242]",children:"Cancel"})]})]}),e.jsx("div",{className:"space-y-3",children:I.length===0?e.jsx("p",{className:"text-[#b5b5b5] text-center py-8",children:"No notes yet"}):I.map(t=>{var s,n,c,m,p,b,v;return e.jsx("div",{className:"bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("div",{className:"flex-1",children:u&&(((s=u.id)==null?void 0:s.value)||u.id)===(((n=t.id)==null?void 0:n.value)||t.id)?e.jsxs("div",{className:"space-y-3",children:[e.jsx("textarea",{value:A,onChange:g=>L(g.target.value),className:"w-full h-24 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:_e,disabled:!A.trim(),className:"px-3 py-1 bg-[#2e7d32] text-[#eaeaea] rounded hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] text-sm",children:"Save"}),e.jsx("button",{onClick:()=>{G(null),L("")},className:"px-3 py-1 text-[#b5b5b5] hover:text-[#eaeaea] text-sm",children:"Cancel"})]})]}):e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-[#eaeaea] mb-2",children:((c=t.content)==null?void 0:c.value)||t.content||((m=t.description)==null?void 0:m.value)||t.description}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:T(((p=t.created_at)==null?void 0:p.value)||t.created_at)})]})}),!u||(((b=u.id)==null?void 0:b.value)||u.id)!==(((v=t.id)==null?void 0:v.value)||t.id)?e.jsxs("div",{className:"flex gap-1 ml-2",children:[e.jsx("button",{onClick:()=>Te(t),className:"text-[#b5b5b5] hover:text-[#2e7d32]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),e.jsx("button",{onClick:()=>J("note",t),className:"text-red-400 hover:text-red-300",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}):null]})},t.id)})})]}),f==="tasks"&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"font-medium text-[#eaeaea]",children:"Tasks"}),e.jsx("button",{onClick:()=>U(!0),className:"px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]",children:"Add Task"})]}),ge&&e.jsxs("div",{className:"bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("textarea",{value:y.content,onChange:t=>D(s=>({...s,content:t.target.value})),placeholder:"Enter task description...",className:"w-full h-20 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"}),e.jsx("input",{type:"datetime-local",value:y.due_date,onChange:t=>D(s=>({...s,due_date:t.target.value})),className:"w-full bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea]"})]}),e.jsxs("div",{className:"flex gap-2 mt-3",children:[e.jsx("button",{onClick:ke,className:"px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]",children:"Save Task"}),e.jsx("button",{onClick:()=>{U(!1),D({content:"",due_date:""})},className:"px-4 py-2 bg-[#363636] text-[#eaeaea] rounded-lg hover:bg-[#424242]",children:"Cancel"})]})]}),e.jsx("div",{className:"space-y-3",children:M.length===0?e.jsx("p",{className:"text-[#b5b5b5] text-center py-8",children:"No tasks yet"}):M.map(t=>{var s,n,c,m,p,b,v,g,_,C,me,xe,ue,he;return e.jsx("div",{className:"bg-[#1e1e1e] p-4 rounded-lg border border-[#363636]",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsx("div",{className:"flex-1",children:h&&(((s=h.id)==null?void 0:s.value)||h.id)===(((n=t.id)==null?void 0:n.value)||t.id)?e.jsxs("div",{className:"space-y-3",children:[e.jsx("textarea",{value:k.content,onChange:W=>w(V=>({...V,content:W.target.value})),className:"w-full h-20 bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea] placeholder-[#b5b5b5] resize-none"}),e.jsx("input",{type:"datetime-local",value:k.due_date,onChange:W=>w(V=>({...V,due_date:W.target.value})),className:"w-full bg-[#161616] border border-[#363636] rounded-lg p-3 text-[#eaeaea]"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:Ce,disabled:!k.content.trim(),className:"px-3 py-1 bg-[#2e7d32] text-[#eaeaea] rounded hover:bg-[#1b5e20] disabled:bg-[#363636] disabled:text-[#666] text-sm",children:"Save"}),e.jsx("button",{onClick:()=>{H(null),w({content:"",due_date:""})},className:"px-3 py-1 text-[#b5b5b5] hover:text-[#eaeaea] text-sm",children:"Cancel"})]})]}):e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-[#eaeaea] mb-2",children:t.description||((m=(c=t.event_details)==null?void 0:c.value)==null?void 0:m.content)||((p=t.content)==null?void 0:p.value)||t.content}),(t.due_date||((v=(b=t.event_details)==null?void 0:b.value)==null?void 0:v.due_date)||((g=t.due_date)==null?void 0:g.value))&&e.jsxs("p",{className:"text-sm text-[#b5b5b5] mb-1",children:["Due: ",T(t.due_date||((C=(_=t.event_details)==null?void 0:_.value)==null?void 0:C.due_date)||((me=t.due_date)==null?void 0:me.value))]}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:["Created: ",T(((xe=t.created_at)==null?void 0:xe.value)||t.created_at)]})]})}),!h||(((ue=h.id)==null?void 0:ue.value)||h.id)!==(((he=t.id)==null?void 0:he.value)||t.id)?e.jsxs("div",{className:"flex gap-1 ml-2",children:[e.jsx("button",{onClick:()=>Ee(t),className:"text-[#b5b5b5] hover:text-[#2e7d32]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),e.jsx("button",{onClick:()=>J("task",t),className:"text-red-400 hover:text-red-300",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}):null]})},t.id)})})]})]})]})]}),j.show&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-[#242424] rounded-lg p-6 max-w-md w-full mx-4 border border-[#363636]",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea] mb-4",children:"Confirm Delete"}),e.jsxs("p",{className:"text-[#b5b5b5] mb-6",children:["Are you sure you want to delete this ",j.type,"? This action cannot be undone."]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:O,className:"px-4 py-2 text-[#b5b5b5] hover:text-[#eaeaea] transition-colors",children:"No"}),e.jsx("button",{onClick:()=>{var t,s;j.type==="note"?ye(((t=j.item.id)==null?void 0:t.value)||j.item.id):we(((s=j.item.id)==null?void 0:s.value)||j.item.id)},className:"px-4 py-2 bg-[#dc3545] text-white rounded hover:bg-[#c82333] transition-colors",children:"Yes"})]})]})})]}):e.jsxs("div",{className:"flex flex-col justify-center items-center min-h-screen",children:[e.jsx("div",{className:"text-[#eaeaea] mb-4",children:"Referral not found"}),e.jsx("button",{onClick:()=>i(-1),className:"px-4 py-2 bg-[#2e7d32] text-[#eaeaea] rounded-lg hover:bg-[#1b5e20]",children:"Go Back"})]})};export{st as default};
