import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r as d}from"./vendor-1c28ea83.js";import{b as w}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";let a=null;const H=({type:o="text",label:x,className:i,placeholder:l="Search",options:h=[],disabled:n=!1,required:f=!1,setValue:g,value:p,onReady:b,timer:j=1e3,showIcon:m=!0})=>{const s=d.useId(),[u,k]=d.useState("");function c(e){const r=e.target.value;g(r),k(r),a&&clearTimeout(a),a=setTimeout(()=>{b(r)},j)}return t.jsxs("div",{className:"grid w-full grid-cols-1 items-start justify-start",children:[t.jsxs("label",{className:"mb-2 block cursor-pointer text-left text-sm font-bold text-gray-700",htmlFor:s,children:[w(x,{casetype:"capitalize",separator:"space"}),f&&t.jsx("sup",{className:"z-[99999] text-[.825rem] text-red-600",children:"*"})]}),o==="dropdown"||o==="select"?t.jsxs("select",{type:o,id:s,disabled:n,placeholder:l,onChange:e=>c(e),value:p||u,className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${i}`,children:[t.jsx("option",{}),h.map((e,r)=>t.jsx("option",{value:e,children:e},r+1))]}):t.jsxs("div",{className:"relative",children:[m&&t.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:t.jsx("svg",{className:"h-4 w-4 text-gray-500 dark:text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20",children:t.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"})})}),t.jsx("input",{type:o,id:s,disabled:n,placeholder:l,onChange:e=>c(e),value:p||u,className:`${m?"pl-10":""} block w-full rounded-lg border border-gray-200 bg-white p-4 text-sm text-black placeholder-black focus:border-blue-500 focus:ring-blue-500 dark:text-gray-400 dark:placeholder-gray-400 ${i}`})]})]})};export{H as default};
