import{j as t}from"./@react-google-maps/api-211df1ae.js";import{L as l,az as u,aA as h,aB as x,aC as y}from"./index-826b1c0e.js";import{M as g}from"./index-dbfe2d0c.js";import{r as n}from"./vendor-1c28ea83.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const s={HTML:"text/html",PDF:"application/pdf",DOCX:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",XLSX:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",JPEG:"image/jpeg",PNG:"image/png"},j={"text/html":"HTML","application/pdf":"PDF","application/vnd.openxmlformats-officedocument.wordprocessingml.document":"DOCX","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":"XLSX","image/jpeg":"JPEG","image/png":"PNG"},w={PDF:t.jsx(h,{}),DOCX:t.jsx(x,{}),XLSX:t.jsx(y,{})},L=({value:e})=>{const[m,p]=n.useState([]),c=async r=>{try{const o=r.map(async a=>{const d=(await fetch(a)).headers.get("content-type");return{href:a,type:d}}),i=await Promise.all(o);p(a=>i)}catch(o){console.error(o)}};return n.useEffect(()=>{try{const r=JSON.parse(e),o=Array.isArray(r)?r:["string"].includes(typeof r)?[r]:[];c(o)}catch{const o=Array.isArray(e)?e:["string"].includes(typeof e)?[e]:[];c(o)}},[e]),t.jsx(n.Fragment,{children:t.jsx(l,{children:t.jsx(g,{display:t.jsxs("button",{type:"button",className:"flex !cursor-pointer items-center justify-center gap-2",children:[t.jsx(u,{className:"h-[1.0313rem] w-[1.0313rem]"}),"View"]}),openOnClick:!0,backgroundColor:"#000",children:t.jsx("div",{className:"flew-wrap grid h-[18.75rem] max-h-[18.75rem] min-h-[18.75rem] w-[18.75rem] min-w-[18.75rem] max-w-[18.75rem]  grid-cols-[repeat(auto-fill,minmax(18.75rem,1fr))]  gap-2 overflow-y-auto",children:m==null?void 0:m.map((r,o)=>t.jsxs("div",{children:[[s.PNG,s.JPEG].includes(r==null?void 0:r.type)?t.jsx("img",{src:r==null?void 0:r.href,alt:r==null?void 0:r.href,className:"h-full w-full object-cover"}):null,[s.PDF,s.DOCX,s.XLSX].includes(r==null?void 0:r.type)?t.jsx("a",{href:r==null?void 0:r.href,target:"_blank",rel:"noreferrer",className:"flex h-full w-full items-center justify-center",children:w[j[r==null?void 0:r.type]]}):null,[s.HTML].includes(r==null?void 0:r.type)?t.jsx("div",{dangerouslySetInnerHTML:{__html:r==null?void 0:r.href}}):null]},o))})})})})},V=n.memo(L);export{V as default};
