import{j as i}from"./@react-google-maps/api-211df1ae.js";import{R as n,r as o,d as w,j as x}from"./vendor-1c28ea83.js";import{A as y,G as b,a as M,M as j,s as k}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const W=()=>{const{dispatch:p}=n.useContext(y),{dispatch:m}=n.useContext(b),[d,c]=o.useState(!1),[s,f]=o.useState(null),t=w(),[u,C]=x(),h=async a=>{try{console.log("Checking Stripe verification...");const e=await a.callRawAPI("/v1/api/dealmaker/user/stripe/account/verify",{},"POST");return console.log("Stripe verification response:",e),e.complete}catch(e){return console.error("Stripe verification check failed:",e),!1}},g=async()=>{let a=new j;try{let e=u.get("token")??null;const r=await a.magicLoginVerify(e);if(console.log("token",e),console.log("result",r),r.error)o.startTransition(()=>{t("/member/login")});else{p({type:"LOGIN",payload:r}),k(m,"Login successful!");const l=await h(a);if(console.log("Stripe verification result:",l),!l){console.log("Stripe not verified, showing modal"),f(`/${r.role}/dashboard`),c(!0);return}o.startTransition(()=>{t(`/${r.role}/dashboard`)})}}catch{o.startTransition(()=>{t("/member/login")})}};n.useEffect(()=>{(async()=>await g())()});const S=()=>{o.startTransition(()=>{t(s||"/member/dashboard")})},v=()=>{c(!1),o.startTransition(()=>{t(s||"/member/dashboard")})};return i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"flex min-h-screen justify-center items-center min-w-full",children:i.jsx("svg",{className:"w-24 h-24 animate-spin",fill:"currentColor",viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg",children:i.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})})}),i.jsx(M,{isOpen:d,onClose:v,onSuccess:S})]})};export{W as default};
