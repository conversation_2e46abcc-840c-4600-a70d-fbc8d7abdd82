import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as a,d as xs,u as hs,R as Be}from"./vendor-1c28ea83.js";import{G as he,M,s as k,T as bs,S as ze,a as vs}from"./index-826b1c0e.js";import{d as ps}from"./@uppy/aws-s3-c23f5c86.js";import"./@craftjs/core-a5d68af1.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const gs="/assets/img-1001f23f.svg",fs=({referralId:u,onClose:S})=>{const{dispatch:n}=a.useContext(he),[R,F]=a.useState(!1),[$,P]=a.useState(""),[f,G]=a.useState({first_name:{value:""},last_name:{value:""},email:{value:""},recommendation:{value:[{first_name:"",last_name:"",email:"",user_id:"12"},{first_name:"",last_name:"",email:"",user_id:"12"},{first_name:"",last_name:"",email:"",user_id:""},{first_name:"",last_name:"",email:"",user_id:""},{first_name:"",last_name:"",email:"",user_id:""}]}}),ae=async N=>{N.preventDefault(),F(!0),P("");const y=f.recommendation.value.filter(d=>d.first_name&&d.last_name&&d.email);if(y.length===0){P("Please fill in at least one recommendation"),F(!1);return}try{const d=new M,C={first_name:f.first_name,last_name:f.last_name,email:f.email.value,onlineAccounts:[],recommendFirstName:y[0].first_name,recommendLastName:y[0].last_name,recommendEmail:y[0].email};console.log("Data being sent to SDK:",JSON.stringify(C,null,2));const z=await d.SubmitReferralRecommendation(u,C);z.error?P(z.message):(k(n,"Referral recommendation submitted successfully!",5e3,"success"),S())}catch(d){P(d.message||"Failed to submit referral recommendation")}finally{F(!1)}};return e.jsx("div",{className:"p-6",children:e.jsxs("form",{onSubmit:ae,className:"space-y-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Your Name *"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:f.first_name.value,onChange:N=>G(y=>({...y,first_name:{value:N.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:f.last_name.value,onChange:N=>G(y=>({...y,last_name:{value:N.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Email *"}),e.jsx("input",{type:"email",value:f.email.value,onChange:N=>G(y=>({...y,email:{value:N.target.value}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Referral #1 *"}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:f.recommendation.value[0].first_name,onChange:N=>G(y=>({...y,recommendation:{value:y.recommendation.value.map((d,C)=>C===0?{...d,first_name:N.target.value}:d)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:f.recommendation.value[0].last_name,onChange:N=>G(y=>({...y,recommendation:{value:y.recommendation.value.map((d,C)=>C===0?{...d,last_name:N.target.value}:d)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Email"}),e.jsx("input",{type:"email",value:f.recommendation.value[0].email,onChange:N=>G(y=>({...y,recommendation:{value:y.recommendation.value.map((d,C)=>C===0?{...d,email:N.target.value}:d)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]",required:!0})]})]}),[1,2,3,4].map(N=>e.jsxs("div",{children:[e.jsxs("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:["Referral #",N+1]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name"}),e.jsx("input",{type:"text",value:f.recommendation.value[N].first_name,onChange:y=>G(d=>({...d,recommendation:{value:d.recommendation.value.map((C,z)=>z===N?{...C,first_name:y.target.value}:C)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name"}),e.jsx("input",{type:"text",value:f.recommendation.value[N].last_name,onChange:y=>G(d=>({...d,recommendation:{value:d.recommendation.value.map((C,z)=>z===N?{...C,last_name:y.target.value}:C)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:["Referral #",N+1," Email"]}),e.jsx("input",{type:"email",value:f.recommendation.value[N].email,onChange:y=>G(d=>({...d,recommendation:{value:d.recommendation.value.map((C,z)=>z===N?{...C,email:y.target.value}:C)}})),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]})]},N)),e.jsxs("div",{style:{padding:"20px"},className:"mt-6 rounded-lg bg-[#242424] text-sm text-[#7dd87d]",children:[e.jsx("p",{className:"mb-2 font-medium",children:"Please note:"}),e.jsx("p",{className:"text-xs text-white",children:"RainmakerOS is not for everyone. This is a vetted premium network designed to connect you with givers, matchers and other business rainmakers."})]}),$&&e.jsx("p",{className:"text-sm text-red-500",children:$}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsx("button",{type:"submit",disabled:R,className:"rounded-lg bg-[#2e7d32] px-6 py-3 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50",children:"Submit Referral"})})]})})},js=u=>u.split(" ").map(S=>S[0]).join("").toUpperCase().slice(0,2),Te=({user:u})=>{var S;return(S=u.photo)!=null&&S.value?e.jsx("img",{src:u.photo.value,alt:u.name.value,className:"object-cover w-8 h-8 rounded-full"}):e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:js(u.name.value)})},Ns=({communityId:u,onClose:S,onSuccess:n})=>{const{dispatch:R}=a.useContext(he),[F,$]=a.useState(""),[P,f]=a.useState([]),[G,ae]=a.useState(!1),[N,y]=a.useState(!1),[d,C]=a.useState([]),[z,H]=a.useState(!1),q=a.useCallback(ps(async m=>{if(m.trim().length<2){f([]);return}try{ae(!0);const c=await new M().SearchUsers(m);c.error||f(c.list||[])}catch(I){console.error("Failed to search users:",I)}finally{ae(!1)}},300),[]),Z=m=>{const I=m.target.value;$(I),y(!0),q(I)},le=m=>{d.some(I=>I.id.value===m.id.value)||C([...d,m]),$(""),f([]),y(!1)},U=m=>{C(d.filter(I=>I.id.value!==m))},ie=async()=>{if(d.length===0){k(R,"Please select at least one user to invite",5e3,"error");return}try{H(!0);const m=new M,I=d.map(c=>m.callRawAPI(`/v1/api/dealmaker/user/community/${u}/add`,{email:c.email.value,user_id:{value:c.id.value}},"POST"));await Promise.all(I),k(R,`Successfully sent ${d.length} invitation${d.length>1?"s":""}`,5e3,"success"),C([]),n&&n(),S&&S()}catch(m){console.error("Failed to send invites:",m),k(R,m.message||"Failed to send invites",5e3,"error")}finally{H(!1)}};return e.jsxs("div",{className:"p-4 bg-[#242424] rounded-lg",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:"Invite New Members"}),e.jsx("button",{onClick:S,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsx("p",{className:"text-[#b5b5b5] mb-4",children:"Search for users to invite to this community. They will receive an email with instructions to login and join."}),d.length>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm text-[#b5b5b5] mb-2",children:"Selected Users:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:d.map(m=>e.jsxs("div",{className:"inline-flex items-center gap-1 rounded bg-[#7dd87d] px-2 py-1 text-sm text-[#1e1e1e]",children:[e.jsx("span",{children:m.name.value}),e.jsx("button",{onClick:()=>U(m.id.value),className:"text-[#1e1e1e] hover:text-red-800 ml-1",children:"×"})]},m.id.value))})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search for users by name or email",value:F,onChange:Z,className:"w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2 text-sm text-[#eaeaea] focus:border-[#7dd87d] focus:outline-none"}),N&&F.trim()!==""&&e.jsx("div",{className:"absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] py-2 shadow-lg max-h-60 overflow-y-auto",children:G?e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"Searching..."}):P.length>0?P.map(m=>{var I;return e.jsx("div",{onClick:()=>le(m),className:"cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#363636]",children:e.jsxs("div",{className:"flex gap-2 items-center",children:[(I=m.avatar)!=null&&I.value?e.jsx("img",{src:m.avatar.value,alt:"",className:"w-6 h-6 rounded-full"}):e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-[#7dd87d] text-sm text-[#1e1e1e]",children:m.name.value.charAt(0)}),e.jsxs("div",{children:[e.jsx("div",{children:m.name.value}),e.jsx("div",{className:"text-sm text-[#b5b5b5]",children:m.email.value})]})]})},m.id.value)}):e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"No users found"})})]}),e.jsx("div",{className:"mt-6 flex justify-end",children:e.jsx("button",{onClick:ie,disabled:z||d.length===0,className:"rounded-lg bg-[#7dd87d] px-4 py-2 text-sm text-[#1e1e1e] hover:bg-[#7dd87d]/90 disabled:opacity-50",children:z?"Sending...":"Send Invites"})})]})},ys=({text:u="",maxWords:S=100})=>{const[n,R]=a.useState(!1);if(!u)return e.jsx("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:"No content available"});const F=u.split(/\s+/),$=F.length>S,P=n?u:F.slice(0,S).join(" ")+($?"...":"");return e.jsx("div",{children:e.jsxs("p",{className:"whitespace-pre-line text-[#b5b5b5]",children:[P,$&&e.jsx("button",{onClick:()=>R(!n),className:"ml-2 text-[#7dd87d] hover:underline",children:n?"Read Less":"Read More"})]})})},ws={Technology:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})}),Business:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),Finance:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),Healthcare:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),Education:e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})},ks=()=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"42",height:"33",viewBox:"0 0 42 33",fill:"none",children:e.jsx("path",{d:"M22.4766 5.79688C23.2781 5.30469 23.8125 4.41172 23.8125 3.40625C23.8125 1.85234 22.5539 0.59375 21 0.59375C19.4461 0.59375 18.1875 1.85234 18.1875 3.40625C18.1875 4.41875 18.7219 5.30469 19.5234 5.79688L15.4945 13.8547C14.8547 15.1344 13.1953 15.5 12.0773 14.607L5.8125 9.59375C6.16406 9.12266 6.375 8.53906 6.375 7.90625C6.375 6.35234 5.11641 5.09375 3.5625 5.09375C2.00859 5.09375 0.75 6.35234 0.75 7.90625C0.75 9.46016 2.00859 10.7188 3.5625 10.7188C3.57656 10.7188 3.59766 10.7188 3.61172 10.7188L6.825 28.3953C7.21172 30.5328 9.075 32.0938 11.2547 32.0938H30.7453C32.918 32.0938 34.7812 30.5398 35.175 28.3953L38.3883 10.7188C38.4023 10.7188 38.4234 10.7188 38.4375 10.7188C39.9914 10.7188 41.25 9.46016 41.25 7.90625C41.25 6.35234 39.9914 5.09375 38.4375 5.09375C36.8836 5.09375 35.625 6.35234 35.625 7.90625C35.625 8.53906 35.8359 9.12266 36.1875 9.59375L29.9227 14.607C28.8047 15.5 27.1453 15.1344 26.5055 13.8547L22.4766 5.79688Z",fill:"#7DD87D"})}),de=u=>{const S=new Date(u),n=S.toLocaleString("default",{month:"short"}),R=S.getDate(),F=S.getFullYear();return`${n} ${R}, ${F}`},Cs=({isOpen:u,onClose:S,post:n})=>{var me,ve,pe,ge,re,ue,ce,fe;const[R,F]=a.useState("details"),[$,P]=a.useState([]),[f,G]=a.useState([]),[ae,N]=a.useState(!1),[y,d]=a.useState(!1),[C,z]=a.useState(""),[H,q]=a.useState({content:"",due_date:""}),[Z,le]=a.useState({show:!1,type:null,item:null}),{dispatch:U}=a.useContext(he);a.useEffect(()=>{u&&n&&(ie(),m())},[u,n]);const ie=async()=>{try{const _=await new M().callRawAPI(`/v1/api/dealmaker/user/notes?referral_id=${n.id.value}`,{},"GET");if(!_.error){const L=(_.list||[]).sort((E,V)=>new Date(V.created_at)-new Date(E.created_at));P(L)}}catch(x){console.error("Failed to load notes:",x)}},m=async()=>{try{const _=await new M().callRawAPI(`/v1/api/dealmaker/user/tasks?referral_id=${n.id.value}`,{},"GET");if(!_.error){const L=(_.list||[]).sort((E,V)=>new Date(E.due_date)-new Date(V.due_date));G(L)}}catch(x){console.error("Failed to load tasks:",x)}},I=async()=>{var x,_,L,E,V;if(C.trim())try{const ne=await new M().callRawAPI("/v1/api/dealmaker/user/notes",{content:{value:C},referral_id:{value:(x=n==null?void 0:n.id)==null?void 0:x.value}},"POST");if(ne.error)throw new Error(ne.message||"Failed to add note");{const Me=new Date().toISOString(),je={id:((L=(_=ne.model)==null?void 0:_.id)==null?void 0:L.value)||Date.now(),description:C,created_at:((V=(E=ne.model)==null?void 0:E.created_at)==null?void 0:V.value)||Me};P(_e=>[je,..._e]),z(""),N(!1),ie(),k(U,"Note added successfully!",5e3,"success")}}catch(oe){console.error("Error adding note:",oe),k(U,oe.message||"Failed to add note",5e3,"error")}},c=(x,_)=>{le({show:!0,type:x,item:_})},K=()=>{le({show:!1,type:null,item:null})},Ce=async x=>{try{const L=await new M().callRawAPI(`/v1/api/dealmaker/user/notes/${x}`,{},"DELETE");if(!L.error)P(E=>E.filter(V=>V.id!==x)),k(U,"Note deleted successfully!",5e3,"success"),K();else throw new Error(L.message||"Failed to delete note")}catch(_){console.error("Error deleting note:",_),k(U,_.message||"Failed to delete note",5e3,"error")}},be=async()=>{var x,_;if(!(!H.content.trim()||!H.due_date))try{const E=await new M().callRawAPI("/v1/api/dealmaker/user/tasks",{content:{value:H.content},due_date:{value:H.due_date},referral_id:{value:(x=n==null?void 0:n.id)==null?void 0:x.value}},"POST");if(E.error)throw new Error(E.message||"Failed to add task");{const V={id:((_=E.model)==null?void 0:_.id)||Date.now(),description:H.content,due_date:H.due_date,created_at:new Date().toISOString(),title:`Task added on ${new Date().toLocaleDateString()}`};G(oe=>[...oe,V]),q({content:"",due_date:""}),d(!1),m(),k(U,"Task added successfully!",5e3,"success")}}catch(L){console.error("Error adding task:",L),k(U,L.message||"Failed to add task",5e3,"error")}},Se=async x=>{try{const L=await new M().callRawAPI(`/v1/api/dealmaker/user/tasks/${x}`,{},"DELETE");if(!L.error)G(E=>E.filter(V=>V.id!==x)),k(U,"Task deleted successfully!",5e3,"success"),K();else throw new Error(L.message||"Failed to delete task")}catch(_){console.error("Error deleting task:",_),k(U,_.message||"Failed to delete task",5e3,"error")}};return!u||!n?null:e.jsxs("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[e.jsxs("div",{className:"flex min-h-full items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:S,"aria-hidden":"true"}),e.jsx("div",{className:"relative z-50 w-full max-w-3xl rounded-lg bg-[#161616] shadow-xl",children:e.jsxs("div",{className:"flex flex-col h-[85vh]",children:[e.jsxs("div",{className:"sticky top-0 z-50 flex justify-between items-center bg-[#161616] p-6 border-b border-[#363636]",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(Te,{user:n.creator}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-[#eaeaea]",children:n.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[n.posted_by.value," -"," ",de(n.created_at.value)]})]})]}),e.jsx("button",{onClick:S,className:"text-[#b5b5b5] hover:text-[#eaeaea] p-2",children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsx("div",{className:"border-b border-[#363636] px-6",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("button",{onClick:()=>F("details"),className:`border-b-2 py-2 px-1 text-sm ${R==="details"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Details"}),e.jsx("button",{onClick:()=>F("notes"),className:`border-b-2 py-2 px-1 text-sm ${R==="notes"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Notes"}),e.jsx("button",{onClick:()=>F("tasks"),className:`border-b-2 py-2 px-1 text-sm ${R==="tasks"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Tasks"})]})}),e.jsxs("div",{className:"flex-1 overflow-y-auto p-6",children:[e.jsx("div",{className:`h-full ${R==="details"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Type"}),e.jsx("p",{className:"text-[#b5b5b5]",children:((me=n==null?void 0:n.type)==null?void 0:me.value)||"N/A"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Description"}),e.jsx(ys,{text:(ve=n==null?void 0:n.description)==null?void 0:ve.value,maxWords:1e3})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Deal Size"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:["$",((pe=n==null?void 0:n.deal_size)==null?void 0:pe.value)||"N/A"]})]}),((ge=n==null?void 0:n.expiration_date)==null?void 0:ge.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Expiration Date"}),e.jsx("p",{className:"text-[#b5b5b5]",children:de(n.expiration_date.value)})]}),((re=n==null?void 0:n.description_image)==null?void 0:re.value)&&e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Attached Image"}),e.jsx("img",{src:n.description_image.value,alt:"Description",className:"max-h-96 rounded-lg object-contain"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Status"}),e.jsx("span",{className:"rounded-full bg-[#2e7d3233] px-3 py-1 text-sm text-[#7dd87d]",children:((ue=n==null?void 0:n.status)==null?void 0:ue.value)||"Unknown"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Referral Type"}),e.jsx("p",{className:"text-[#b5b5b5]",children:((ce=n==null?void 0:n.referral_type)==null?void 0:ce.value)||"N/A"})]}),((fe=n==null?void 0:n.recommendations)==null?void 0:fe.value)&&n.recommendations.value.length>0&&e.jsxs("div",{children:[e.jsxs("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:["Referrals (",n.recommendations.value.length,")"]}),e.jsx("div",{className:"space-y-2",children:n.recommendations.value.map((x,_)=>{var L,E;return e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsx("p",{className:"text-[#eaeaea]",children:(E=(L=x.user)==null?void 0:L.name)==null?void 0:E.value})},_)})})]})]})}),e.jsx("div",{className:`h-full ${R==="notes"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-[#eaeaea]",children:"Notes"}),e.jsxs("button",{onClick:()=>N(!0),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Add Note"]})]}),ae&&e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsx("textarea",{value:C,onChange:x=>z(x.target.value),placeholder:"Write your note...",className:"mb-4 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-[#eaeaea]",rows:3}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>N(!1),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:I,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Add Note"})]})]}),e.jsx("div",{className:"space-y-2",children:$.map(x=>e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea]",children:x.description}),e.jsx("p",{className:"mt-2 text-sm text-[#b5b5b5]",children:de(x.created_at)})]}),e.jsx("button",{onClick:()=>c("note",x),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},x.id))})]})}),e.jsx("div",{className:`h-full ${R==="tasks"?"block":"hidden"}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h4",{className:"text-lg font-medium text-[#eaeaea]",children:"Tasks"}),e.jsxs("button",{onClick:()=>d(!0),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"Add Task"]})]}),y&&e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Task Title"}),e.jsx("input",{type:"text",value:H.content,onChange:x=>q({...H,content:x.target.value}),placeholder:"Enter task title...",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Due Date"}),e.jsx("input",{type:"date",value:H.due_date,onChange:x=>q({...H,due_date:x.target.value}),className:"h-10 w-full rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea]"})]}),e.jsxs("div",{className:"flex justify-end gap-2",children:[e.jsx("button",{onClick:()=>d(!1),className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea]",children:"Cancel"}),e.jsx("button",{onClick:be,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Add Task"})]})]}),e.jsx("div",{className:"space-y-2",children:f.length===0?e.jsx("p",{className:"text-center text-[#b5b5b5] py-4",children:"No tasks yet. Create your first task!"}):f.map(x=>e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-[#eaeaea] font-medium",children:x.description}),e.jsxs("p",{className:"mt-1 text-sm text-[#b5b5b5]",children:["Due: ",de(x.due_date)]})]}),e.jsx("button",{onClick:()=>c("task",x),className:"text-[#b5b5b5] hover:text-[#dc3545]",children:e.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})},x.id))})]})})]})]})})]}),Z.show&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-[#242424] rounded-lg p-6 max-w-md w-full mx-4 border border-[#363636]",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea] mb-4",children:"Confirm Delete"}),e.jsxs("p",{className:"text-[#b5b5b5] mb-6",children:["Are you sure you want to delete this ",Z.type,"? This action cannot be undone."]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:K,className:"px-4 py-2 text-[#b5b5b5] hover:text-[#eaeaea] transition-colors",children:"No"}),e.jsx("button",{onClick:()=>{Z.type==="note"?Ce(Z.item.id):Se(Z.item.id)},className:"px-4 py-2 bg-[#dc3545] text-white rounded hover:bg-[#c82333] transition-colors",children:"Yes"})]})]})})]})},Zs=()=>{const{dispatch:u}=a.useContext(he),S=xs(),n=hs(),[R,F]=a.useState("joined"),[$,P]=a.useState([]),[f,G]=a.useState([]),[ae,N]=a.useState([]),[y,d]=a.useState(!0),[C,z]=a.useState(!1),[H,q]=a.useState(!0),[Z,le]=a.useState([]),[U,ie]=a.useState("");a.useState(!1);const[m,I]=a.useState("");a.useState(!1),a.useState([]),a.useState(""),a.useState(!1),a.useState(null),a.useState(!0);const[c,K]=a.useState(null),[Ce,be]=a.useState(!1),[Se,me]=a.useState(null),[ve,pe]=a.useState([]);a.useState(!1),a.useState("card"),a.useState({card_number:"",exp_month:"",exp_year:"",cvc:"",name:"",is_default:!1}),a.useState({}),a.useState(!1);const[ge,re]=a.useState(!1),[ue,ce]=a.useState(!1),[fe,x]=a.useState([]),[_,L]=a.useState(!1),[E,V]=a.useState(!1),[oe,ne]=a.useState(null),[Me,je]=a.useState(!1),[_e,Ee]=a.useState(null);console.log(C);const $e=async()=>{try{await new M().callRawAPI("/v1/api/dealmaker/user/payment-methods/account/set-default",{},"POST"),console.log("Payment method set as default")}catch(s){console.error("Failed to set payment method as default:",s)}},Ge=async()=>{try{const t=await new M().GetPaymentMethods();t.list&&t.list.length>0&&t.list.find(o=>o.is_default.value===0)&&await $e()}catch(s){console.error("Failed to check payment methods:",s)}};a.useEffect(()=>{const s=async()=>{var r,o;if((r=n.state)!=null&&r.showCommunity&&((o=n.state)!=null&&o.communityId)){try{const h=await new M().GetCommunityDetail(n.state.communityId);h.error||(K(h.model),z(!0),Ue(n.state.communityId))}catch(b){console.error("Error loading community:",b),k(u,"Failed to load community",5e3,"error")}window.history.replaceState({},document.title)}};(async()=>{await s(),Ae(),Oe(),as(),await Ge()})()},[]),a.useEffect(()=>{(async()=>{var h;const t=new URLSearchParams(n.search),r=t.get("join_id"),o=t.get("community_invitation"),b=t.get("ref");if(r)try{const g=await new M().GetCommunityDetail(r);if(g.error)k(u,"Community not found",5e3,"error");else{K(g.model),re(!0);const T=window.location.pathname;window.history.replaceState({},"",T)}}catch(v){console.error("Error fetching community:",v),k(u,"Failed to load community information",5e3,"error")}if(o&&b)try{const g=await new M().GetCommunityDetail(o);if(g.error)k(u,"Community not found",5e3,"error");else{K(g.model),re(!0),k(u,`You've been invited to join ${(h=g.model.title)==null?void 0:h.value}. Do you want to join?`,8e3,"info");const T=window.location.pathname;window.history.replaceState({},"",T)}}catch(v){console.error("Error fetching community:",v),k(u,"Failed to load community information",5e3,"error")}})()},[n.search,u]);const De=(s,t)=>{let r;return function(...b){const h=()=>{clearTimeout(r),s(...b)};clearTimeout(r),r=setTimeout(h,t)}},Ae=async()=>{try{q(!0);const t=await new M().GetCommunities();if(t.error)console.error("Error loading communities:",t.message),ie(t.message);else{const r=t.list||[];console.log("Loaded communities:",r.length),G(r),P(r)}}catch(s){console.error("Exception loading communities:",s),ie(s.message||"Failed to load communities")}finally{q(!1)}},Fe=s=>{if(!s.trim()||!f.length){P(f);return}const t=s.toLowerCase(),r=f.filter(o=>{var T,B,Q,se,Y,X;const b=(B=(T=o.title)==null?void 0:T.value)==null?void 0:B.toLowerCase().includes(t),h=(se=(Q=o.description)==null?void 0:Q.value)==null?void 0:se.toLowerCase().includes(t),v=(X=(Y=o.industry_name)==null?void 0:Y.value)==null?void 0:X.toLowerCase().includes(t);return o={...o,_searchMatches:{title:b,description:h,industry:v}},b||h||v});console.log(`Filtered ${f.length} communities to ${r.length} results for query "${t}"`),(r.length>0||!s.trim())&&P(r)},He=Be.useCallback(De(s=>Fe(s),300),[f]);a.useEffect(()=>{f.length>0&&m.trim()&&Fe(m)},[m,f]),a.useEffect(()=>{console.log("Tab changed to:",R),f.length>0&&(P(f),m&&I(""))},[R]);const Ve=s=>{const t=s.target.value;I(t),!t.trim()&&f.length>0?P(f):t.trim()&&He(t)},Oe=async()=>{try{d(!0);const t=await new M().GetCommunityFeed({limit:10});t.error||N(t.list||[])}catch(s){console.error("Failed to load activity:",s)}finally{d(!1)}},We=async s=>{console.log("Viewing community:",s);try{q(!0);const t=new M,r=await t.GetCommunityDetail(s);r.error||K(r.model);const o=await t.GetCommunityReferrals(s);o.error||le(o.list||[]),z(!0),F("joined")}catch(t){console.error("Failed to load community:",t),k(u,"Failed to load community",5e3,"error")}finally{q(!1)}},Je=()=>{var s,t,r,o,b,h;return console.log("Rendering community view:",{selectedCommunity:c,communityPosts:Z}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-[#363636] text-[#eaeaea] text-lg",children:(t=(s=c.title)==null?void 0:s.value)==null?void 0:t.charAt(0)}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:c.title.value}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:c.industry_name.value})]})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("button",{onClick:()=>{Ne(c.id.value),ce(!0)},className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})}),"Members (",((r=c.member_count)==null?void 0:r.value)||0,")"]}),!(((o=c.title)==null?void 0:o.value)==="RainmakerOS"&&((b=c.id)==null?void 0:b.value)===1)&&e.jsxs("button",{onClick:()=>S(`/member/communities/${c.id.value}/chat`),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),"Chat"]}),e.jsxs("button",{onClick:()=>{z(!1),K(null),le([])},className:"flex items-center gap-2 text-sm text-[#7dd87d]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back"]})]})]}),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#161616] p-6",children:[e.jsx("h3",{className:"mb-2 text-lg font-medium text-[#eaeaea]",children:"About"}),e.jsx("p",{className:"text-[#b5b5b5]",children:(h=c.description)==null?void 0:h.value})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Community Opportunities"})}),Z.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg border border-[#363636] bg-[#161616] p-8",children:[e.jsx("svg",{className:"mb-4 h-12 w-12 text-[#b5b5b5]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2"})}),e.jsx("p",{className:"text-lg font-medium text-[#eaeaea]",children:"No updates yet"})]}):e.jsx("div",{className:"grid grid-cols-1 gap-6",children:Z.map(v=>e.jsxs("div",{className:"flex flex-col gap-2 rounded-lg border border-[#363636] bg-[#161616] p-4",children:[e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx(Te,{user:v.creator}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-[#eaeaea]",children:v.title.value}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[v.creator.name.value," -"," ",de(v.created_at.value)]})})]})]}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{onClick:()=>ds(v),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#242424]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})}),"View Details"]})})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-[#eaeaea]",children:"Description"}),e.jsx("p",{className:"text-[#b5b5b5]",children:v.description.value})]}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsx("button",{onClick:()=>Xe(v.id.value),className:"flex items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-white",children:"Refer"}),e.jsx("button",{onClick:()=>es(v.id.value),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:"Chat"}),e.jsx("button",{onClick:()=>ss(v.id.value),className:"flex items-center gap-2 rounded-lg border border-[#363636] bg-[#161616] px-4 py-2 text-sm text-[#eaeaea]",children:"Repost"})]})]},v.id.value))})]}),e.jsx(Ie,{isOpen:ue,onClose:()=>ce(!1)})]})},Ue=async s=>{try{q(!0);const r=await new M().GetCommunityReferrals(s);r.error||le(r.list||[])}catch(t){console.error("Failed to load posts:",t)}finally{q(!1)}},qe=()=>{var s,t;return e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("div",{className:"flex gap-4 items-center",children:c?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-[#363636] text-[#eaeaea] text-lg",children:(t=(s=c.title)==null?void 0:s.value)==null?void 0:t.charAt(0)}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:c.title.value}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:c.industry_name.value})]})]}):e.jsxs(e.Fragment,{children:[e.jsx("img",{src:gs,alt:"RainmakerOS LLC",className:"w-12 h-12 rounded-full"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-[#eaeaea]",children:"RainmakerOS LLC"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Community Management"})]})]})}),e.jsxs("div",{className:"flex gap-4 items-center",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:m,onChange:Ve,placeholder:"Search communities...",className:"h-[42px] w-64 border border-[#363636] bg-[#242424] pl-4 pr-10 text-[#eaeaea] placeholder-[#666]"}),m?e.jsx("button",{onClick:()=>{I(""),P(f)},className:"absolute top-1/2 right-3 -translate-y-1/2 cursor-pointer",title:"Clear search",children:e.jsx("svg",{className:"w-4 h-4 text-[#b5b5b5] hover:text-[#eaeaea]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):e.jsx("div",{style:{top:"50%",right:"10px",transform:"translateY(-50%)"},className:"absolute right-3 text-white",children:e.jsx("svg",{className:"w-4 h-4",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M15.7955 15.8111L21 21M18 10.5C18 14.6421 14.6421 18 10.5 18C6.35786 18 3 14.6421 3 10.5C3 6.35786 6.35786 3 10.5 3C14.6421 3 18 6.35786 18 10.5Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsxs("button",{onClick:os,className:"flex h-[42px] items-center gap-2 rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:[e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Post Opportunity"]})]})]})},Ze=()=>e.jsxs("div",{className:"mb-6 border-b border-[#363636]",children:[e.jsx("button",{onClick:()=>F("joined"),className:`mr-6 border-b-2 pb-2 text-sm ${R==="joined"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"My Communities"}),e.jsx("button",{onClick:()=>{F("join"),z(!1)},className:`mr-6 border-b-2 pb-2 text-sm ${R==="join"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Join a Community"}),e.jsx("button",{onClick:()=>S("/member/communities/create"),className:`border-b-2 pb-2 text-sm ${R==="create"?"border-[#7dd87d] text-[#7dd87d]":"border-transparent text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Create a Community"})]}),Ye=()=>e.jsxs("div",{className:"flex flex-1",children:[e.jsxs("div",{style:{width:"65%"},className:"",children:[e.jsxs("div",{style:{padding:"16px",marginRight:"16px"},className:"mb-8 border-b border-[#363636] bg-black",children:[e.jsx("h2",{className:"mb-4 text-sm font-semibold text-[#eaeaea]",children:"Your Communities"}),e.jsx("div",{className:"space-y-4",children:H?[...Array(2)].map((s,t)=>e.jsx(ze,{className:"w-full h-24 rounded-lg"},t)):$.filter(s=>{var t;return((t=s.is_member)==null?void 0:t.value)===!0}).sort((s,t)=>{var b,h,v,g;const r=((b=s.title)==null?void 0:b.value)==="RainmakerOS"&&((h=s.id)==null?void 0:h.value)===1,o=((v=t.title)==null?void 0:v.value)==="RainmakerOS"&&((g=t.id)==null?void 0:g.value)===1;return r&&!o?-1:!r&&o?1:0}).map(s=>{var t,r,o,b,h,v,g;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-[#363636] bg-[#242424] p-4",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[((t=s.title)==null?void 0:t.value)==="RainmakerOS"&&((r=s.id)==null?void 0:r.value)===1&&e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M16 12V4a1 1 0 00-.5-.87L12 1 8.5 3.13A1 1 0 008 4v8l-2 2v1h12v-1l-2-2z"})}),((o=s.privacy)==null?void 0:o.value)==="private"&&e.jsx("svg",{className:"h-5 w-5 text-[#b5b5b5]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-[#eaeaea] flex items-center gap-2",children:s.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[((b=s.privacy_settings)==null?void 0:b.value.who_can_find)==="hidden"?"Hidden":((h=s.privacy_settings)==null?void 0:h.value.who_can_find)==="private"?"Private":"Public"," ","Community • ",((v=s.member_count)==null?void 0:v.value)||0," ","members"]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>We(s.id.value),className:"rounded-lg border border-[#363636] bg-[#242424] px-4 py-2 text-sm text-[#eaeaea]",children:"View"}),((g=s.is_admin)==null?void 0:g.value)===!0&&e.jsx("button",{onClick:()=>rs(s.id.value),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Manage"})]})]},s.id.value)})})]}),e.jsxs("div",{style:{padding:"16px",marginRight:"16px"},className:"mb-8 border-b border-[#363636] bg-black",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-sm font-semibold text-[#eaeaea]",children:"Available Communities"}),e.jsx("button",{onClick:()=>F("join"),className:"text-sm text-[#7dd87d]",children:"View All"})]}),e.jsx("div",{className:"space-y-4",children:H?[...Array(2)].map((s,t)=>e.jsx(ze,{className:"w-full h-24 rounded-lg"},t)):$.filter(s=>{var t;return((t=s.is_member)==null?void 0:t.value)===!1}).map(s=>{var t,r,o;return e.jsxs("div",{className:"flex items-center justify-between rounded-lg border border-[#363636] bg-[#242424] p-4",children:[e.jsxs("div",{className:"flex gap-3 items-center",children:[((t=s.privacy)==null?void 0:t.value)==="private"&&e.jsx("svg",{className:"h-5 w-5 text-[#b5b5b5]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-[#eaeaea]",children:s.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[((r=s.privacy)==null?void 0:r.value)==="private"?"Private":"Public"," ","Community • ",((o=s.member_count)==null?void 0:o.value)||0," ","members"]})]})]}),e.jsx("button",{onClick:()=>Pe(s),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Join"})]},s.id.value)})})]})]}),Ke()]}),Ke=()=>e.jsx("div",{style:{width:"35%"},className:"space-y-6",children:e.jsxs("div",{className:"p-4 bg-black rounded",children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold text-[#eaeaea]",children:"Recent Activity"}),e.jsx("div",{className:"space-y-4",children:ae.map(s=>e.jsxs("div",{className:"border-b border-[#363636] pb-4",children:[e.jsx("p",{className:"text-sm text-[#eaeaea]",children:s.title.value}),e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:de(new Date(s.created_at.value))})]},s.id.value))})]})}),Pe=s=>{K(s),re(!0)},Qe=()=>e.jsxs("div",{className:"w-full",children:[e.jsx("div",{className:"flex justify-between items-center mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"All Communities"})}),m.trim()&&$.length>0&&e.jsxs("div",{className:"mb-4 p-2 bg-[#1e1e1e] border border-[#363636] rounded-lg",children:[e.jsxs("p",{className:"text-[#eaeaea]",children:["Found ",e.jsx("span",{className:"font-semibold text-[#7dd87d]",children:$.length}),' communities matching "',m,'"']}),e.jsx("p",{className:"text-[#b5b5b5] text-sm mt-1",children:"Searching in title, description, and industry fields"})]}),m.trim()&&$.length===0&&!H&&e.jsxs("div",{className:"flex flex-col items-center justify-center p-8 text-center mb-4",children:[e.jsx("svg",{className:"w-16 h-16 mb-4 text-[#363636]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),e.jsx("h3",{className:"text-xl font-medium text-[#eaeaea] mb-2",children:"No communities found"}),e.jsxs("p",{className:"text-[#b5b5b5]",children:['No communities matching "',m,'" were found in titles, descriptions, or industry fields. Try a different search term.']})]}),e.jsx("div",{className:"grid grid-cols-3 gap-6",children:$.filter(s=>{var t;return((t=s.is_member)==null?void 0:t.value)===!1}).map(s=>{var t,r,o,b;return e.jsxs("div",{className:"flex flex-col rounded-lg border border-[#363636] bg-[#161616] p-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("div",{className:"mr-4 mb-2",children:ws[(t=s.industry)==null?void 0:t.value]||e.jsx("svg",{className:"h-8 w-8 text-[#7dd87d]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsx("h3",{className:"mb-1 font-semibold text-[#eaeaea]",children:s.title.value}),e.jsxs("p",{className:"text-sm text-[#b5b5b5]",children:[((r=s.member_count)==null?void 0:r.value)||0," members"]})]}),((o=s.privacy)==null?void 0:o.value)==="private"&&e.jsx("svg",{className:"h-5 w-5 text-[#b5b5b5]",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M12 17a2 2 0 100-4 2 2 0 000 4zm6-9a2 2 0 012 2v10a2 2 0 01-2 2H6a2 2 0 01-2-2V10a2 2 0 012-2h1V6a5 5 0 0110 0v2h1zm-6-5a3 3 0 00-3 3v2h6V6a3 3 0 00-3-3z"})})]}),e.jsx("p",{className:"mb-6 flex-grow text-sm text-[#b5b5b5]",children:(b=s.description)==null?void 0:b.value}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("p",{className:"text-sm text-[#7dd87d]",children:"Active"}),e.jsx("button",{onClick:()=>Pe(s),className:" rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]",children:"Join Community"})]})]},s.id.value)})})]}),Xe=async s=>{ne(s),V(!0)},es=async s=>{S(`/member/chat/${s}`)},ss=s=>{me(s),be(!0)},ts=({isOpen:s,onClose:t,referralId:r,communities:o})=>{if(!s)return null;const{dispatch:b}=a.useContext(he),[h,v]=a.useState("community"),[g,T]=a.useState(""),[B,Q]=a.useState(null),[se,Y]=a.useState(""),[X,ee]=a.useState([]),[i,O]=a.useState(!1),[W,D]=a.useState(!1),[te,ye]=a.useState(!1),Re=a.useCallback(De(async p=>{if(p.trim().length<2){ee([]),D(!1);return}try{O(!0);const ke=await new M().SearchUsers(p);ke.error||(ee(ke.list||[]),D(!0))}catch(J){console.error("Failed to search users:",J)}finally{O(!1)}},300),[]),we=p=>{const J=p.target.value;Y(J),Re(J)},Le=p=>{Q(p),Y(p.name.value||p.email.value),D(!1),ee([])};a.useEffect(()=>{const p=J=>{W&&!J.target.closest(".user-search-container")&&D(!1)};return document.addEventListener("mousedown",p),()=>{document.removeEventListener("mousedown",p)}},[W]);const xe=async()=>{if(!(h==="community"&&!g)&&!(h==="user"&&!B)){ye(!0);try{const p=new M,J={referral_id:r};h==="community"?J.community_id=g:J.user_id=B.id.value,await p.RepostReferral(J),k(b,`Referral reposted to ${h==="community"?"community":"user"} successfully!`,5e3,"success"),t(!0)}catch(p){k(b,p.message||"Failed to repost referral",5e3,"error")}finally{ye(!1)}}};return e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>!te&&t()}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-[#161616] p-6 shadow-xl",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea]",children:"Repost Referral"}),e.jsx("button",{onClick:()=>!te&&t(),className:"text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50",disabled:te,children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),e.jsxs("div",{className:"mb-6 flex gap-4",children:[e.jsxs("label",{className:"flex items-center gap-2 cursor-pointer",children:[e.jsx("input",{type:"radio",name:"repostType",value:"community",checked:h==="community",onChange:p=>v(p.target.value),className:"text-[#2e7d32] focus:ring-[#2e7d32]"}),e.jsx("span",{className:"text-[#eaeaea]",children:"Repost to Community"})]}),e.jsxs("label",{className:"flex items-center gap-2 cursor-pointer",children:[e.jsx("input",{type:"radio",name:"repostType",value:"user",checked:h==="user",onChange:p=>v(p.target.value),className:"text-[#2e7d32] focus:ring-[#2e7d32]"}),e.jsx("span",{className:"text-[#eaeaea]",children:"Repost to Person"})]})]}),h==="community"&&e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[#b5b5b5]",children:"Select Community"}),e.jsxs("select",{value:g,onChange:p=>T(p.target.value),className:"h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#242424] bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNkw4IDEwTDEyIDYiIHN0cm9rZT0iI2I1YjViNSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==')] bg-[length:16px_16px] bg-[right_16px_center] bg-no-repeat px-4 text-[#eaeaea]",required:!0,children:[e.jsx("option",{value:"",children:"Select a community"}),o.map(p=>e.jsx("option",{value:p.id.value,children:p.title.value},p.id.value))]})]}),h==="user"&&e.jsxs("div",{className:"mb-6 user-search-container relative",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-[#b5b5b5]",children:"Search for Person"}),e.jsx("input",{type:"text",value:se,onChange:we,placeholder:"Type name or email to search...",className:"h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] focus:border-[#2e7d32] focus:outline-none"}),W&&e.jsx("div",{className:"absolute z-10 mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] py-2 shadow-lg max-h-60 overflow-y-auto",children:i?e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"Searching..."}):X.length>0?X.map(p=>e.jsxs("div",{onClick:()=>Le(p),className:"cursor-pointer px-4 py-2 text-[#eaeaea] hover:bg-[#2e7d32] flex items-center gap-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:p.name.value?p.name.value.charAt(0):p.email.value.charAt(0)}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:p.name.value||"No name"}),e.jsx("div",{className:"text-sm text-[#b5b5b5]",children:p.email.value})]})]},p.id.value)):e.jsx("div",{className:"px-4 py-2 text-[#b5b5b5]",children:"No users found"})}),B&&e.jsxs("div",{className:"mt-2 flex items-center gap-3 rounded-lg border border-[#363636] bg-[#1e1e1e] p-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm text-white",children:B.name.value?B.name.value.charAt(0):B.email.value.charAt(0)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-[#eaeaea]",children:B.name.value||"No name"}),e.jsx("div",{className:"text-sm text-[#b5b5b5]",children:B.email.value})]}),e.jsx("button",{onClick:()=>{Q(null),Y("")},className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),e.jsxs("div",{className:"flex gap-4 justify-end",children:[e.jsx("button",{onClick:()=>!te&&t(),disabled:te,className:"rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#b5b5b5] hover:text-[#eaeaea] disabled:opacity-50",children:"Cancel"}),e.jsx("button",{onClick:xe,disabled:te||h==="community"&&!g||h==="user"&&!B,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-white hover:bg-[#1b5e20] disabled:opacity-50",children:te?"Reposting...":"Repost"})]})]})]})},as=async()=>{try{const t=await new M().GetJoinedCommunities();t.error||pe(t.list||[])}catch(s){console.error("Failed to load communities:",s)}},ls=({isOpen:s,onClose:t,community:r})=>{if(!s)return null;const[o,b]=a.useState(1),[h,v]=a.useState(!1);a.useState(!1);const[g,T]=a.useState({}),[B,Q]=a.useState(!1),[se,Y]=a.useState([]),[X,ee]=a.useState(!1);a.useState(null);const[i,O]=a.useState({firstName:"",lastName:"",industry:"",employeeCount:"",annualRevenue:"",goals:{buildPartnerships:!1,gainAccess:!1,getResources:!1,learnStrategies:!1,findSolutions:!1,other:!1},otherGoal:"",businessChallenge:"",majorBreakthrough:"",whyJoin:"",meetingCommitment:"",additionalNotes:"",agreeToTerms:!1,agreeToGuidelines:!1});a.useEffect(()=>{const w=async()=>{try{const j=await new M().callRawAPI("/v1/api/dealmaker/user/details",{},"GET");!j.error&&j.model&&O(us=>({...us,firstName:j.model.first_name.value||"",lastName:j.model.last_name.value||""}))}catch(l){console.error("Failed to fetch user details:",l)}},A=async()=>{try{const j=await new M().callRawAPI("/v1/api/dealmaker/industries",{},"GET");!j.error&&j.data?Y(j.data):k(u,j.message||"Failed to load industries",5e3,"error")}catch(l){k(u,l.message||"Failed to load industries",5e3,"error")}};w(),A()},[]);const W=(w,A)=>{T(l=>({...l,[w]:!0})),O(l=>({...l,[w]:A}))},D=w=>{var A;return(g[w]||B)&&!((A=i[w])!=null&&A.trim())},te=()=>(g.goals||B)&&!Object.values(i.goals).some(w=>w===!0),ye=()=>(g.otherGoal||B)&&i.goals.other&&!i.otherGoal.trim(),Re=()=>{Q(!0),we()&&b(2)},we=()=>!(!i.firstName.trim()||!i.lastName.trim()||!i.industry.trim()||!i.employeeCount.trim()||!i.annualRevenue.trim()||!i.businessChallenge.trim()||!i.majorBreakthrough.trim()||!i.whyJoin.trim()||!i.meetingCommitment.trim()||!Object.values(i.goals).some(A=>A===!0)||i.goals.other&&!i.otherGoal.trim()),Le=()=>{var w,A;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:["Join ",(w=r==null?void 0:r.title)==null?void 0:w.value," Community"]}),xe(),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"First Name *"}),e.jsx("input",{type:"text",value:i.firstName,onChange:l=>W("firstName",l.target.value),onBlur:()=>T(l=>({...l,firstName:!0})),className:`h-10 w-full rounded-lg border ${D("firstName")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,placeholder:"Enter first name",required:!0}),D("firstName")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"First name is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Last Name *"}),e.jsx("input",{type:"text",value:i.lastName,onChange:l=>W("lastName",l.target.value),onBlur:()=>T(l=>({...l,lastName:!0})),className:`h-10 w-full rounded-lg border ${D("lastName")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,placeholder:"Enter last name",required:!0}),D("lastName")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Last name is required"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What industry is your business in? *"}),e.jsxs("select",{value:i.industry,onChange:l=>W("industry",l.target.value),onBlur:()=>T(l=>({...l,industry:!0})),className:`h-10 w-full appearance-none rounded-lg border ${D("industry")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,required:!0,children:[e.jsx("option",{value:"",children:"Select your industry"}),se.map(l=>e.jsx("option",{value:l.id,children:l.name},l.id))]}),D("industry")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Industry is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"How many employees does your business have? *"}),e.jsxs("select",{value:i.employeeCount,onChange:l=>W("employeeCount",l.target.value),onBlur:()=>T(l=>({...l,employeeCount:!0})),className:`h-10 w-full appearance-none rounded-lg border ${D("employeeCount")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,required:!0,children:[e.jsx("option",{value:"",children:"Select range"}),e.jsx("option",{value:"1-10",children:"1-10"}),e.jsx("option",{value:"11-50",children:"11-50"}),e.jsx("option",{value:"51-200",children:"51-200"}),e.jsx("option",{value:"201-500",children:"201-500"}),e.jsx("option",{value:"500+",children:"500+"})]}),D("employeeCount")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Employee count is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What's your approximate annual revenue? *"}),e.jsxs("select",{value:i.annualRevenue,onChange:l=>W("annualRevenue",l.target.value),onBlur:()=>T(l=>({...l,annualRevenue:!0})),className:`h-10 w-full appearance-none rounded-lg border ${D("annualRevenue")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] px-4 text-[#eaeaea]`,required:!0,children:[e.jsx("option",{value:"",children:"Select range"}),e.jsx("option",{value:"under_100k",children:"under 100K"}),e.jsx("option",{value:"100k_500k",children:"100K - 500K"}),e.jsx("option",{value:"500k_1m",children:"500K - 1M"}),e.jsx("option",{value:"1m_5m",children:"1M - 5M"}),e.jsx("option",{value:"5m_plus",children:"5M+"})]}),D("annualRevenue")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Annual revenue is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What are your top 3 goals for joining this community? *"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:i.goals.buildPartnerships,onChange:l=>O(j=>({...j,goals:{...j.goals,buildPartnerships:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Build partnerships to grow my business"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:i.goals.gainAccess,onChange:l=>O(j=>({...j,goals:{...j.goals,gainAccess:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Gain access to investors and capital partners"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:i.goals.getResources,onChange:l=>O(j=>({...j,goals:{...j.goals,getResources:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Get access to new resources and opportunities"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:i.goals.learnStrategies,onChange:l=>O(j=>({...j,goals:{...j.goals,learnStrategies:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Learn strategies to leverage relationships"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:i.goals.findSolutions,onChange:l=>O(j=>({...j,goals:{...j.goals,findSolutions:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Find solutions to specific business challenges"})]}),e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:i.goals.other,onChange:l=>O(j=>({...j,goals:{...j.goals,other:l.target.checked}})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Other:"})]}),i.goals.other&&e.jsx("input",{type:"text",value:i.otherGoal,onChange:l=>O(j=>({...j,otherGoal:l.target.value})),className:"mt-2 h-10 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]",placeholder:"Please specify"})]}),te()&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please select at least one goal"}),ye()&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please specify your other goal"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"What is your biggest business challenge that needs solving sooner rather than later? *"}),e.jsx("textarea",{value:i.businessChallenge,onChange:l=>W("businessChallenge",l.target.value),onBlur:()=>T(l=>({...l,businessChallenge:!0})),className:`h-32 w-full rounded-lg border ${D("businessChallenge")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] p-4 text-[#eaeaea]`,placeholder:"Describe your challenge...",required:!0}),D("businessChallenge")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Business challenge is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"If you could achieve one major breakthrough in your business this year, what would it be? *"}),e.jsx("textarea",{value:i.majorBreakthrough,onChange:l=>W("majorBreakthrough",l.target.value),onBlur:()=>T(l=>({...l,majorBreakthrough:!0})),className:`h-32 w-full rounded-lg border ${D("majorBreakthrough")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] p-4 text-[#eaeaea]`,placeholder:"Describe your desired breakthrough...",required:!0}),D("majorBreakthrough")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Major breakthrough is required"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:["Why do you want to be part of ",(A=r==null?void 0:r.title)==null?void 0:A.value,"? *"]}),e.jsx("textarea",{value:i.whyJoin,onChange:l=>W("whyJoin",l.target.value),onBlur:()=>T(l=>({...l,whyJoin:!0})),className:`h-32 w-full rounded-lg border ${D("whyJoin")?"border-red-500":"border-[#363636]"} bg-[#1e1e1e] p-4 text-[#eaeaea]`,placeholder:"Share your reasons...",required:!0}),D("whyJoin")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"This field is required"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"How much time can you realistically commit to meeting with members and engaging in potential deals each month? *"}),e.jsx("div",{className:"space-y-2",children:["1-2 hours","3-5 hours","6+ hours"].map(l=>e.jsxs("label",{className:"flex items-center",children:[e.jsx("input",{type:"radio",name:"meetingCommitment",value:l.toLowerCase(),checked:i.meetingCommitment===l.toLowerCase(),onChange:j=>W("meetingCommitment",j.target.value),onBlur:()=>T(j=>({...j,meetingCommitment:!0})),className:"mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:l})]},l))}),D("meetingCommitment")&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:"Please select your time commitment"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm text-[#b5b5b5]",children:"Is there anything else you'd like us to know?"}),e.jsx("textarea",{value:i.additionalNotes,onChange:l=>W("additionalNotes",l.target.value),className:"h-32 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]",placeholder:"Share any additional information..."})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:Re,disabled:B&&!we(),className:"rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})})]})},xe=()=>e.jsx("div",{className:"flex justify-center items-center mb-6",children:[1,2,3].map(w=>e.jsxs(Be.Fragment,{children:[e.jsx("div",{onClick:()=>w<o&&b(w),className:`h-2 w-2 rounded-full ${o>=w?"cursor-pointer bg-[#2e7d32]":"bg-[#363636]"}`}),w<3&&e.jsx("div",{className:`h-[2px] w-16 ${o>w?"bg-[#2e7d32]":"bg-[#363636]"}`})]},w))}),p=()=>{var w;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex gap-2 items-center",children:[e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",viewBox:"0 0 20 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("g",{clipPath:"url(#clip0_1_486)",children:e.jsx("path",{d:"M4.5 0C5.16304 0 5.79893 0.263392 6.26777 0.732233C6.73661 1.20107 7 1.83696 7 2.5C7 3.16304 6.73661 3.79893 6.26777 4.26777C5.79893 4.73661 5.16304 5 4.5 5C3.83696 5 3.20107 4.73661 2.73223 4.26777C2.26339 3.79893 2 3.16304 2 2.5C2 1.83696 2.26339 1.20107 2.73223 0.732233C3.20107 0.263392 3.83696 0 4.5 0ZM16 0C16.663 0 17.2989 0.263392 17.7678 0.732233C18.2366 1.20107 18.5 1.83696 18.5 2.5C18.5 3.16304 18.2366 3.79893 17.7678 4.26777C17.2989 4.73661 16.663 5 16 5C15.337 5 14.7011 4.73661 14.2322 4.26777C13.7634 3.79893 13.5 3.16304 13.5 2.5C13.5 1.83696 13.7634 1.20107 14.2322 0.732233C14.7011 0.263392 15.337 0 16 0ZM0 9.33438C0 7.49375 1.49375 6 3.33437 6H4.66875C5.16562 6 5.6375 6.10938 6.0625 6.30312C6.02187 6.52812 6.00313 6.7625 6.00313 7C6.00313 8.19375 6.52812 9.26562 7.35625 10C7.35 10 7.34375 10 7.33437 10H0.665625C0.3 10 0 9.7 0 9.33438ZM12.6656 10C12.6594 10 12.6531 10 12.6438 10C13.475 9.26562 13.9969 8.19375 13.9969 7C13.9969 6.7625 13.975 6.53125 13.9375 6.30312C14.3625 6.10625 14.8344 6 15.3313 6H16.6656C18.5063 6 20 7.49375 20 9.33438C20 9.70312 19.7 10 19.3344 10H12.6656ZM7 7C7 6.20435 7.31607 5.44129 7.87868 4.87868C8.44129 4.31607 9.20435 4 10 4C10.7956 4 11.5587 4.31607 12.1213 4.87868C12.6839 5.44129 13 6.20435 13 7C13 7.79565 12.6839 8.55871 12.1213 9.12132C11.5587 9.68393 10.7956 10 10 10C9.20435 10 8.44129 9.68393 7.87868 9.12132C7.31607 8.55871 7 7.79565 7 7ZM4 15.1656C4 12.8656 5.86562 11 8.16562 11H11.8344C14.1344 11 16 12.8656 16 15.1656C16 15.625 15.6281 16 15.1656 16H4.83437C4.375 16 4 15.6281 4 15.1656Z",fill:"#7dd87d"})})}),e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Community Guidelines"})]}),e.jsx("p",{className:"ml-4 text-sm text-[#b5b5b5]",children:r.title.value}),xe(),e.jsx("div",{style:{maxHeight:"400px",scrollbarWidth:"thin",scrollbarColor:"#7dd87d #242424",overflowY:"auto"},className:"rounded-lg bg-[#242424] p-4",children:e.jsx("div",{className:"text-[#b5b5b5]",dangerouslySetInnerHTML:{__html:((w=r.requirements)==null?void 0:w.value)||"No specific guidelines provided."}})}),e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("label",{className:"flex items-start",children:[e.jsx("input",{type:"checkbox",checked:i.agreeToGuidelines,onChange:A=>O(l=>({...l,agreeToGuidelines:A.target.checked})),className:"mt-1 mr-2"}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"I agree to follow the community guidelines"})]}),e.jsxs("label",{className:"flex items-start",children:[e.jsx("input",{type:"checkbox",checked:i.agreeToTerms,onChange:A=>O(l=>({...l,agreeToTerms:A.target.checked})),className:"mt-1 mr-2"}),e.jsxs("span",{className:"text-sm text-[#eaeaea]",children:["I agree to the"," ",e.jsx("a",{href:"#",className:"text-[#7dd87d]",children:"Terms & Conditions"})]})]})]}),e.jsxs("div",{className:"flex gap-4 justify-end",children:[e.jsx("button",{onClick:()=>b(1),className:"rounded-lg border border-[#363636] px-6 py-2 text-sm text-[#b5b5b5]",children:"Back"}),e.jsx("button",{onClick:()=>b(3),disabled:!i.agreeToGuidelines||!i.agreeToTerms,className:"rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white disabled:opacity-50",children:"Next"})]})]})},J=async()=>{try{v(!0);const w=new M,A=await w.callRawAPI("/v1/api/dealmaker/user/stripe/account/verify",{},"POST");if(A.error||!A.complete){v(!1),ee(!0);return}const l=await w.JoinCommunity({community_id:{value:r.id.value},first_name:{value:i.firstName},last_name:{value:i.lastName},industry_id:i.industry?parseInt(i.industry,10):null,company_size:{value:i.employeeCount},meeting_commitment:{value:i.meetingCommitment},additional_notes:{value:i.additionalNotes},goals:{value:{networking:i.goals.buildPartnerships,businessDevelopment:i.goals.gainAccess,learningAndDevelopment:i.goals.getResources}},skip_payment:!1});l.error?k(u,l.message,5e3,"error"):(k(u,"Successfully joined community!",5e3,"success"),t(!0),a.startTransition(()=>{S("/member/communities")}),re(!1))}catch(w){k(u,w.message||"Failed to join community",5e3,"error")}finally{v(!1)}},ke=async()=>{ee(!1),await J()},cs=()=>{ee(!1)},ms=()=>{var w,A,l;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col justify-center items-center",children:[e.jsx(ks,{}),e.jsx("h2",{className:"mt-4 text-center text-xl font-semibold text-[#eaeaea]",children:"To Join a Community, Please Pay Your Community Fee"})]}),xe(),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6",children:[e.jsxs("div",{className:"mb-6 text-center",children:[e.jsx("h3",{className:"text-lg font-medium text-[#eaeaea] mb-4",children:"Community Fee"}),e.jsxs("div",{className:"text-3xl font-bold text-[#a3eca3]",children:["$",Number(((w=r.subscription_fee)==null?void 0:w.value)||((l=(A=r.privacy_settings)==null?void 0:A.value)==null?void 0:l.subscription_fee)||"0").toFixed(2)]})]}),e.jsx("div",{className:"flex flex-col gap-3 mt-6",children:e.jsx("button",{onClick:()=>J(),disabled:h,className:"w-full rounded-lg bg-[#2e7d32] px-6 py-3 text-center text-sm font-medium text-white hover:bg-[#1b5e20] disabled:opacity-50",children:h?"Processing...":"Pay Now"})})]})]})};return e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>!h&&t()}),e.jsxs("div",{style:{scrollbarWidth:"thin",scrollbarColor:"#7dd87d #242424"},className:"relative z-50 max-h-[90vh]  w-full max-w-2xl overflow-y-auto rounded-lg bg-[#252525] p-6 shadow-xl",children:[o===1&&Le(),o===2&&p(),o===3&&ms()]}),e.jsx(vs,{isOpen:X,onSuccess:ke,onClose:cs})]})},rs=s=>{S(`/member/communities/edit/${s}`)},Ne=async s=>{try{L(!0);const r=await new M().callRawAPI(`/v1/api/dealmaker/user/community/${s}/users`,{},"GET");r.error||x(r.list||[])}catch(t){console.error("Failed to load members:",t),L(!1),k(u,t.message,5e3,"error")}finally{L(!1)}},ns=async s=>{try{const t=new M,r={user_id:{value:s},remove:{value:!0},role:{value:"member"}},o=await t.callRawAPI(`/v1/api/dealmaker/user/community/${c.id.value}/update`,r,"POST");if(o.error){k(u,o.message||"Failed to remove member",5e3,"error");return}Ne(c.id.value)}catch(t){console.error("Failed to remove member:",t),k(u,t.message||"Failed to remove member",5e3,"error")}},is=async(s,t)=>{try{const r=new M,o={user_id:{value:s},remove:{value:!1},role:{value:t}},b=await r.callRawAPI(`/v1/api/dealmaker/user/community/${c.id.value}/update`,o,"POST");if(b.error){k(u,b.message||"Failed to update role",5e3,"error");return}Ne(c.id.value)}catch(r){console.error("Failed to update role:",r),k(u,r.message||"Failed to update role",5e3,"error")}},Ie=({isOpen:s,onClose:t})=>{var b,h,v;const[r,o]=a.useState(!1);return s?e.jsxs("div",{className:"flex fixed inset-0 z-50 justify-center items-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:t}),e.jsxs("div",{className:"relative z-50 w-full max-w-4xl rounded-lg bg-[#161616] p-6 shadow-xl",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea] mr-4",children:"Community Members"}),e.jsx("button",{onClick:()=>o(!r),className:"rounded-lg bg-[#7dd87d] px-4 py-1.5 text-sm text-[#1e1e1e] hover:bg-[#7dd87d]/90",children:"Add Members"})]}),e.jsx("button",{onClick:t,className:"text-[#b5b5b5] hover:text-[#eaeaea]",children:e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]}),r&&e.jsx(Ns,{communityId:c.id.value,onClose:()=>o(!1),onSuccess:()=>Ne(c.id.value)}),_?e.jsx("div",{className:"flex justify-center",children:e.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-[#7dd87d] border-t-transparent"})}):e.jsx("div",{className:"max-h-[60vh] overflow-y-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{className:"border-b border-[#363636] text-left",children:e.jsxs("tr",{children:[e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Member"}),e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Role"}),e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Industry"}),e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Joined"}),!(((b=c.title)==null?void 0:b.value)==="RainmakerOS"&&((h=c.id)==null?void 0:h.value)===1)&&((v=c.is_admin)==null?void 0:v.value)===!0&&e.jsx("th",{className:"pb-3 text-sm font-medium text-[#b5b5b5]",children:"Actions"})]})}),e.jsx("tbody",{children:fe.map(g=>{var T,B,Q,se,Y,X,ee;return e.jsxs("tr",{className:"border-b border-[#363636]",children:[e.jsx("td",{className:"py-4",children:e.jsxs("div",{className:"flex gap-3 items-center",children:[e.jsx(Te,{user:g}),e.jsx("button",{onClick:()=>S(`/member/user/${g.id.value}`),className:"text-[#eaeaea] hover:text-[#7dd87d] transition-colors cursor-pointer text-left",children:g.name.value})]})}),e.jsx("td",{className:"py-4",children:!(((T=c.title)==null?void 0:T.value)==="RainmakerOS"&&((B=c.id)==null?void 0:B.value)===1)&&((Q=c.is_admin)==null?void 0:Q.value)===!0?e.jsxs("select",{value:g.role.value,onChange:i=>is(g.id.value,i.target.value),className:"rounded bg-[#242424] px-2 py-1 text-sm text-[#eaeaea]",children:[e.jsx("option",{value:"member",children:"Member"}),e.jsx("option",{value:"admin",children:"Admin"})]}):e.jsx("span",{className:"text-[#eaeaea] capitalize",children:g.role.value})}),e.jsx("td",{className:"py-4 text-[#eaeaea]",children:((se=g.industry)==null?void 0:se.value)||"N/A"}),e.jsx("td",{className:"py-4 text-[#eaeaea]",children:de(g.joined_at.value)}),!(((Y=c.title)==null?void 0:Y.value)==="RainmakerOS"&&((X=c.id)==null?void 0:X.value)===1)&&((ee=c.is_admin)==null?void 0:ee.value)===!0&&e.jsx("td",{className:"py-4",children:e.jsx("button",{onClick:()=>ns(g.id.value),className:"text-red-500 hover:text-red-400",children:"Remove"})})]},`${g.id.value}-${g.joined_at.value}`)})})]})})]})]}):null},os=()=>{var t;const s=(t=c==null?void 0:c.id)==null?void 0:t.value;S("/member/referrals/add",{state:{referralType:"community referral",communityId:s}})},ds=s=>{Ee(s),je(!0)};return e.jsxs("div",{className:"min-h-screen bg-[#1e1e1e] p-4",children:[e.jsx("style",{children:`
          input[type="date"]::-webkit-calendar-picker-indicator,
          input[type="datetime-local"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover,
          input[type="datetime-local"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}),U&&e.jsx(bs,{message:U,type:"error"}),E&&e.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:e.jsxs("div",{className:"flex min-h-full items-center justify-center p-4",children:[e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:()=>{V(!1),ne(null)},"aria-hidden":"true"}),e.jsx("div",{className:"relative z-50 w-full max-w-3xl transform overflow-hidden",children:e.jsx("div",{className:"relative bg-[#1e1e1e] rounded-lg shadow-xl",children:e.jsx(fs,{referralId:oe,onClose:()=>{V(!1),ne(null)}})})})]})}),qe(),Ze(),C?Je():R==="join"?Qe():Ye(),e.jsx(ls,{isOpen:ge,onClose:()=>re(!1),community:c}),e.jsx(Ie,{isOpen:ue,onClose:()=>ce(!1)}),e.jsx(ts,{isOpen:Ce,onClose:s=>{be(!1),me(null),s&&Ae()},referralId:Se,communities:ve}),e.jsx(Cs,{isOpen:Me,onClose:()=>je(!1),post:_e})]})};export{Zs as default};
