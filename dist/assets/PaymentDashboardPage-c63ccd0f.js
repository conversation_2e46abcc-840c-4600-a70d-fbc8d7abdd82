import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as d}from"./vendor-1c28ea83.js";import{G as T,M as v,s as h}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const he=()=>{const[B,V]=d.useState(!0),{dispatch:c}=d.useContext(T),[g,N]=d.useState({totalEarned:0,totalPaid:0,pendingPayout:0,availableToWithdraw:0}),[L,A]=d.useState([]),[_,y]=d.useState([]),[j,P]=d.useState(""),[E,D]=d.useState(!1),[C,M]=d.useState(!1),[F,H]=d.useState({card_number:"",exp_month:"",exp_year:"",cvc:"",name:"",is_default:!1}),[o,I]=d.useState({}),[k,S]=d.useState(!1),b=t=>new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(t),W=async t=>{try{const a=new v;await a.callRawAPI("/v1/api/dealmaker/user/payment-methods/account/set-default",{},"POST");const r=await a.GetPaymentMethods();y(r.list),console.log("Payment method set as default")}catch(a){console.error("Failed to set payment method as default:",a),h(c,"Failed to set payment method as default",5e3,"error")}};d.useEffect(()=>{(async()=>{try{const a=new v,[r,p,w]=await Promise.all([a.GetEarningsStats(),a.GetReferralActivity(),a.GetPaymentMethods()]);N({totalEarned:r.model.total_earned.value,totalPaid:r.model.total_paid.value,pendingPayout:r.model.pending_payout.value,availableToWithdraw:r.model.available_to_withdraw.value}),A(p.list);const f=w.list;if(y(f),f.length>0){const s=f.find(l=>l.is_default.value===0);s&&await W(s.id.value)}}catch(a){h(c,a.message,5e3,"error")}finally{V(!1)}})()},[c]);const $=async()=>{if(!j||isNaN(j)){h(c,"Please enter a valid amount",5e3,"error");return}try{M(!0);const t=new v;if((await t.callRawAPI("/v1/api/dealmaker/user/stripe/account/verify",{},"POST")).complete){(await t.callRawAPI("/v1/api/dealmaker/user/stripe/transfer",{amount:parseFloat(j)},"POST")).error?h(c,"Payment failed",5e3,"error"):(h(c,"Withdrawal initiated successfully",5e3,"success"),P(""));const p=await t.GetEarningsStats();N({totalEarned:p.model.total_earned.value,totalPaid:p.model.total_paid.value,pendingPayout:p.model.pending_payout.value,availableToWithdraw:p.model.available_to_withdraw.value})}else{const r=await t.callRawAPI("/v1/api/dealmaker/user/stripe/onboarding",{},"POST");if(console.log("onbb",r),r&&r.url)window.location.href=r.url;else throw new Error("Failed to get Stripe onboarding link")}}catch(t){console.error("Withdrawal error:",t),h(c,t.message||"Failed to process withdrawal",5e3,"error")}finally{M(!1)}},Y=({type:t})=>t==="paypal"?e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M20.4 9.6H3.6C3.27 9.6 3 9.33 3 9V6C3 5.67 3.27 5.4 3.6 5.4H20.4C20.73 5.4 21 5.67 21 6V9C21 9.33 20.73 9.6 20.4 9.6Z"})}):t==="connected_account"||t==="account"?e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17C15.24 5.06 14.32 5 13.4 5H10.6C9.68 5 8.76 5.06 7.83 5.17L10.5 2.5L9 1L3 7V9C3 10.1 3.9 11 5 11V19C5 20.1 5.9 21 7 21H17C18.1 21 19 20.1 19 19V11C20.1 11 21 10.1 21 9Z"})}):e.jsx("svg",{className:"h-5 w-5 text-[#7dd87d]",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"})}),Z=()=>{const[t,a]=d.useState(F),r=s=>{const{name:l,value:n,type:x,checked:m}=s.target;if(l==="card_number"){const u=n.replace(/\D/g,"").substring(0,16).replace(/(\d{4})(?=\d)/g,"$1 ");a(R=>({...R,[l]:u}))}else if(l==="exp_month"){const i=n.replace(/\D/g,"").substring(0,2);a(u=>({...u,[l]:i}))}else if(l==="exp_year"){const i=n.replace(/\D/g,"").substring(0,4);a(u=>({...u,[l]:i}))}else if(l==="cvc"){const i=n.replace(/\D/g,"").substring(0,4);a(u=>({...u,[l]:i}))}else a(i=>({...i,[l]:x==="checkbox"?m:n}))},p=s=>{const l=s.replace(/\s/g,"");if(!/^\d+$/.test(l))return!1;let n=0,x=!1;for(let m=l.length-1;m>=0;m--){let i=parseInt(l.charAt(m));x&&(i*=2,i>9&&(i-=9)),n+=i,x=!x}return n%10===0},w=()=>{const s={},l=new Date,n=l.getFullYear(),x=l.getMonth()+1;if(!t.card_number.trim())s.card_number="Card number is required";else{const m=t.card_number.replace(/\s/g,"");/^\d{13,19}$/.test(m)?p(m)||(s.card_number="Invalid card number"):s.card_number="Card number should have 13-19 digits"}return t.exp_month.trim()?/^(0[1-9]|1[0-2])$/.test(t.exp_month)||(s.exp_month="Invalid month (01-12)"):s.exp_month="Expiration month is required",t.exp_year.trim()?/^\d{4}$/.test(t.exp_year)?parseInt(t.exp_year)<n&&(s.exp_year="Card has expired"):s.exp_year="Invalid year format (YYYY)":s.exp_year="Expiration year is required",!s.exp_month&&!s.exp_year&&parseInt(t.exp_year)===n&&parseInt(t.exp_month)<x&&(s.exp_month="Card has expired"),t.cvc.trim()?/^\d{3,4}$/.test(t.cvc)||(s.cvc="Invalid CVC"):s.cvc="CVC is required",t.name.trim()||(s.name="Cardholder name is required"),s},f=async s=>{s.preventDefault();const l=w();if(I(l),Object.keys(l).length===0){S(!0);try{const n=new v,x={type:{value:"card"},is_default:{value:t.is_default},card_number:{value:t.card_number.replace(/\s/g,"")},exp_month:{value:t.exp_month},exp_year:{value:t.exp_year},cvc:{value:t.cvc},name:{value:t.name}};await n.AddPaymentMethod(x),h(c,"Payment method added successfully",5e3,"success");const m=await n.GetPaymentMethods();y(m.list),D(!1),H({card_number:"",exp_month:"",exp_year:"",cvc:"",name:"",is_default:!1})}catch(n){h(c,n.message,5e3,"error")}finally{S(!1)}}};return e.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75",children:e.jsxs("div",{className:"w-[400px] rounded-lg bg-[#161616] p-6",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-semibold text-[#eaeaea]",children:"Add Payment Method"}),e.jsx("button",{onClick:()=>D(!1),className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#242424] text-[#b5b5b5] hover:bg-[#363636] hover:text-[#eaeaea] transition-colors",type:"button",children:e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex items-center gap-2 rounded border border-[#7dd87d] p-2",children:[e.jsx("div",{className:"h-4 w-4 rounded-full border border-[#7dd87d] flex items-center justify-center",children:e.jsx("div",{className:"h-2 w-2 rounded-full bg-[#7dd87d]"})}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:"Credit/Debit Card"})]})}),e.jsxs("form",{onSubmit:f,children:[e.jsxs("div",{className:"mb-3",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Card Number"}),e.jsx("input",{type:"text",name:"card_number",value:t.card_number,onChange:r,placeholder:"1234 5678 9012 3456",className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),o.card_number&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:o.card_number})]}),e.jsxs("div",{className:"mb-3 flex gap-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Exp. Month"}),e.jsx("input",{type:"text",name:"exp_month",value:t.exp_month,onChange:r,placeholder:"MM",className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),o.exp_month&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:o.exp_month})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Exp. Year"}),e.jsx("input",{type:"text",name:"exp_year",value:t.exp_year,onChange:r,placeholder:"YYYY",className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),o.exp_year&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:o.exp_year})]}),e.jsxs("div",{className:"w-20",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"CVC"}),e.jsx("input",{type:"text",name:"cvc",value:t.cvc,onChange:r,placeholder:"123",className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),o.cvc&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:o.cvc})]})]}),e.jsxs("div",{className:"mb-3",children:[e.jsx("label",{className:"mb-1 block text-sm text-[#b5b5b5]",children:"Cardholder Name"}),e.jsx("input",{type:"text",name:"name",value:t.name,onChange:r,placeholder:"John Doe",className:"w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"}),o.name&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:o.name})]}),e.jsxs("div",{className:"mt-4 flex items-center",children:[e.jsx("input",{type:"checkbox",id:"is_default",name:"is_default",checked:t.is_default,onChange:r,className:"h-4 w-4 rounded bg-[#242424]"}),e.jsx("label",{htmlFor:"is_default",className:"ml-2 text-sm text-[#b5b5b5]",children:"Set as default payment method"})]}),e.jsx("button",{type:"submit",disabled:k,className:"mt-6 w-full rounded bg-[#2e7d32] py-2 text-sm text-[#eaeaea] disabled:opacity-50",children:k?"Adding...":"Add Payment Method"})]})]})})};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-[#eaeaea]",children:"Your Earnings"}),e.jsx("p",{className:"text-sm text-[#b5b5b5]",children:"Track and manage your earnings efficiently"})]}),e.jsxs("div",{className:"mt-6 flex justify-between items-center",children:[e.jsxs("div",{style:{width:"23%"},className:"rounded-lg bg-[#161616] p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-[#b5b5b5]",children:"Total Earned"}),e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"8",height:"14",viewBox:"0 0 10 16",fill:"none",children:[e.jsx("g",{"clip-path":"url(#clip0_4_4584)",children:e.jsx("path",{d:"M4.99998 0C5.55311 0 5.99998 0.446875 5.99998 1V2.11562C6.04998 2.12187 6.09686 2.12812 6.14686 2.1375C6.15936 2.14062 6.16873 2.14062 6.18123 2.14375L7.68123 2.41875C8.22498 2.51875 8.58436 3.04063 8.48436 3.58125C8.38436 4.12187 7.86248 4.48438 7.32186 4.38438L5.83748 4.1125C4.85936 3.96875 3.99686 4.06563 3.39061 4.30625C2.78436 4.54688 2.54061 4.87812 2.48436 5.18437C2.42186 5.51875 2.46873 5.70625 2.52186 5.82188C2.57811 5.94375 2.69373 6.08125 2.92186 6.23438C3.43123 6.56875 4.21248 6.7875 5.22498 7.05625L5.31561 7.08125C6.20936 7.31875 7.30311 7.60625 8.11561 8.1375C8.55936 8.42812 8.97811 8.82187 9.23748 9.37187C9.50311 9.93125 9.55936 10.5563 9.43748 11.2219C9.22186 12.4094 8.40311 13.2031 7.38748 13.6187C6.95936 13.7937 6.49373 13.9062 5.99998 13.9625V15C5.99998 15.5531 5.55311 16 4.99998 16C4.44686 16 3.99998 15.5531 3.99998 15V13.9094C3.98748 13.9062 3.97186 13.9062 3.95936 13.9031H3.95311C3.19061 13.7844 1.93748 13.4563 1.09373 13.0813C0.590609 12.8562 0.362484 12.2656 0.587484 11.7625C0.812484 11.2594 1.40311 11.0312 1.90623 11.2563C2.55936 11.5469 3.63436 11.8344 4.25623 11.9312C5.25311 12.0781 6.07498 11.9937 6.63123 11.7656C7.15936 11.55 7.39998 11.2375 7.46873 10.8625C7.52811 10.5312 7.48123 10.3406 7.42811 10.225C7.36873 10.1 7.25311 9.9625 7.02186 9.80937C6.50936 9.475 5.72498 9.25625 4.70936 8.9875L4.62186 8.96562C3.73123 8.72812 2.63748 8.4375 1.82498 7.90625C1.38123 7.61562 0.965609 7.21875 0.706234 6.66875C0.443734 6.10938 0.390609 5.48438 0.515609 4.81875C0.740609 3.625 1.63436 2.85 2.64998 2.44688C3.06561 2.28125 3.52186 2.16875 3.99998 2.10313V1C3.99998 0.446875 4.44686 0 4.99998 0Z",fill:"#7DD87D"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_4_4584",children:e.jsx("path",{d:"M0 0H10V16H0V0Z",fill:"white"})})})]})]}),e.jsxs("h3",{className:"mt-1 text-lg font-semibold text-[#eaeaea]",children:["$",b(g.totalEarned)]})]}),e.jsxs("div",{style:{width:"23%"},className:"rounded-lg bg-[#161616] p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-[#b5b5b5]",children:"Total Paid"}),e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("g",{"clip-path":"url(#clip0_4_4591)",children:e.jsx("path",{d:"M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16ZM11.5312 6.53125L7.53125 10.5312C7.2375 10.825 6.7625 10.825 6.47188 10.5312L4.47188 8.53125C4.17813 8.2375 4.17813 7.7625 4.47188 7.47188C4.76562 7.18125 5.24062 7.17813 5.53125 7.47188L7 8.94063L10.4688 5.46875C10.7625 5.175 11.2375 5.175 11.5281 5.46875C11.8187 5.7625 11.8219 6.2375 11.5281 6.52812L11.5312 6.53125Z",fill:"#7DD87D"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_4_4591",children:e.jsx("path",{d:"M0 0H16V16H0V0Z",fill:"white"})})})]})]}),e.jsxs("h3",{className:"mt-1 text-lg font-semibold text-[#eaeaea]",children:["$",b(g.totalPaid)]})]}),e.jsxs("div",{style:{width:"23%"},className:"rounded-lg bg-[#161616] p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-[#b5b5b5]",children:"Pending Payout"}),e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("g",{"clip-path":"url(#clip0_4_4598)",children:e.jsx("path",{d:"M8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0ZM7.25 3.75V8C7.25 8.25 7.375 8.48438 7.58437 8.625L10.5844 10.625C10.9281 10.8562 11.3938 10.7625 11.625 10.4156C11.8562 10.0687 11.7625 9.60625 11.4156 9.375L8.75 7.6V3.75C8.75 3.33437 8.41562 3 8 3C7.58437 3 7.25 3.33437 7.25 3.75Z",fill:"#FFD700"})}),e.jsx("defs",{children:e.jsx("clipPath",{id:"clip0_4_4598",children:e.jsx("path",{d:"M0 0H16V16H0V0Z",fill:"white"})})})]})]}),e.jsxs("h3",{className:"mt-1 text-lg font-semibold text-[#eaeaea]",children:["$",b(g.pendingPayout)]})]}),e.jsxs("div",{style:{width:"23%"},className:"rounded-lg bg-[#161616] p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-[#b5b5b5]",children:"Available to Withdraw"}),e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("path",{d:"M0 0H16V16H0V0Z",stroke:"#E5E7EB"}),e.jsx("path",{d:"M2 1C0.896875 1 0 1.89688 0 3V13C0 14.1031 0.896875 15 2 15H14C15.1031 15 16 14.1031 16 13V6C16 4.89687 15.1031 4 14 4H2.5C2.225 4 2 3.775 2 3.5C2 3.225 2.225 3 2.5 3H14C14.5531 3 15 2.55313 15 2C15 1.44687 14.5531 1 14 1H2ZM13 8.5C13.2652 8.5 13.5196 8.60536 13.7071 8.79289C13.8946 8.98043 14 9.23478 14 9.5C14 9.76522 13.8946 10.0196 13.7071 10.2071C13.5196 10.3946 13.2652 10.5 13 10.5C12.7348 10.5 12.4804 10.3946 12.2929 10.2071C12.1054 10.0196 12 9.76522 12 9.5C12 9.23478 12.1054 8.98043 12.2929 8.79289C12.4804 8.60536 12.7348 8.5 13 8.5Z",fill:"#7DD87D"})]})]}),e.jsxs("h3",{className:"mt-1 text-lg font-semibold text-[#eaeaea]",children:["$",b(g.availableToWithdraw)]})]})]}),e.jsxs("div",{className:"mt-8 flex gap-6",children:[e.jsxs("div",{className:"flex-1 rounded-lg bg-[#161616] p-4",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Activity"}),e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-[#b5b5b5]",children:[e.jsx("th",{className:"pb-2",children:"Date"}),e.jsx("th",{className:"pb-2",children:"Activity"}),e.jsx("th",{className:"pb-2",children:"Status"}),e.jsx("th",{className:"pb-2 text-right",children:"Amount"})]})}),e.jsx("tbody",{children:L.map((t,a)=>e.jsxs("tr",{children:[e.jsx("td",{className:"py-2 text-sm text-[#eaeaea]",children:new Date(t.date.value).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}),e.jsx("td",{className:"py-2 text-sm text-[#eaeaea]",children:t.referral.value.split("_").join(" ")}),e.jsx("td",{className:"py-2",children:e.jsx("span",{style:{color:t.status.value==="completed"?"#7dd87d":"#ffd700"},className:"rounded px-2 py-1 text-xs",children:t.status.value.charAt(0).toUpperCase()+t.status.value.slice(1)})}),e.jsxs("td",{className:"py-2 text-right text-sm text-[#eaeaea]",children:["$",b(t.amount.value)]})]},`${t.id.value}-${t.date.value}-${a}`))})]})]}),e.jsxs("div",{className:"w-[400px] space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-[#161616] p-4",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Withdraw Funds"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{style:{top:"50%",left:"10px",transform:"translateY(-50%)"},className:"absolute text-[#eaeaea]",children:"$"}),e.jsx("input",{type:"text",value:j,onChange:t=>P(t.target.value),placeholder:"Enter amount",className:"w-full rounded bg-[#242424] px-8 py-2 text-[#eaeaea] placeholder-[#b5b5b5]",disabled:C})]}),e.jsx("button",{onClick:$,disabled:C,className:"mt-4 w-full rounded bg-[#2e7d32] py-2 text-sm text-[#eaeaea] disabled:opacity-50",children:C?"Processing...":"Withdraw Funds"})]}),e.jsxs("div",{className:"rounded-lg bg-[#161616] p-4 mt-4",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold text-[#eaeaea]",children:"Payment Methods"}),_.length===0?e.jsxs("div",{className:"text-center py-6",children:[e.jsx("div",{className:"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-[#2e7d32]",children:e.jsx("svg",{className:"h-8 w-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),e.jsx("h3",{className:"text-lg font-semibold text-[#eaeaea] mb-2",children:"Connect Your Payment Account"}),e.jsx("p",{className:"text-sm text-[#b5b5b5] mb-4",children:"Connect your Stripe account to receive payments and withdraw your earnings."}),e.jsx("button",{onClick:async()=>{try{const a=await new v().callRawAPI("/v1/api/dealmaker/user/stripe/onboarding",{},"POST");if(a&&a.url)window.location.href=a.url;else throw new Error("Failed to get Stripe onboarding link")}catch(t){console.error("Stripe Connect error:",t),h(c,t.message||"Failed to connect Stripe account",5e3,"error")}},className:"rounded bg-[#2e7d32] px-6 py-3 text-sm font-medium text-[#eaeaea] hover:bg-[#266d2a]",children:"Connect Stripe Account"})]}):_.map(t=>{var a;return e.jsxs("div",{className:"mb-3 flex items-center justify-between rounded bg-[#242424] p-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Y,{type:t.type.value}),e.jsx("span",{className:"text-sm text-[#eaeaea]",children:t.description?t.description.value:t.type.value==="card"?"Card (Ending in "+(((a=t.last4)==null?void 0:a.value)||"****")+")":t.type.value==="connected_account"||t.type.value==="account"?"Stripe Connected Account":"Payment Method"})]}),t.is_default.value===1&&e.jsx("span",{className:"text-xs text-[#7dd87d]",children:"Default"})]},t.id.value)})]})]})]}),E&&e.jsx(Z,{})]})};export{he as default};
