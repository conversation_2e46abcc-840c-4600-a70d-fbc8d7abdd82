import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as u,r as m,u as d,L as p,k as h}from"./vendor-1c28ea83.js";import{G as b,A as y,ae as g,L as j}from"./index-826b1c0e.js";import{B as A}from"./index-82d4da4e.js";import"./pdf-lib-623decea.js";import{b as l}from"./index.esm-1ac45320.js";import{P as L}from"./index-4969721e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const s={LINK:"link",DROPDOWN:"dropdown"},w=[{to:"/manager/items",text:"View Items",type:s.LINK,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"items"},{to:"/manager/assembly",text:"View Assembly",type:s.LINK,value:"assembly"}],K=[{to:"/manager/pending_orders",text:"Pending Orders",type:s.LINK,value:"pending orders"},{to:"/manager/cancelled_orders",text:"Cancelled Orders",type:s.LINK,value:"cancelled orders"},{to:"/manager/completed-orders",text:"Completed Orders",type:s.LINK,value:"completed orders"}],k=[{to:"/manager/inventory",text:"View Inventory",type:s.LINK,value:"inventory"},{to:"/manager/purge_requests",text:"Purge Requests",type:s.LINK,value:"purge requests"},{to:"/manager/hold_requests",text:"Hold Requests",type:s.LINK,value:"hold requests"},{to:"/manager/cycle_counts",text:"Cylce Count Requests",type:s.LINK,value:"cylce count requests"},{to:"/manager/transfer_inventory",text:"Transfer Inventory",type:s.LINK,value:"transfer"}],r=[{to:"/manager/company",text:"Companies",type:s.LINK,value:"company"},{text:"Address Book",value:"address_book",to:"/manager/address_book",type:s.LINK},{to:"/manager/warehouses",text:"Warehouses",type:s.LINK,value:"warehouses"},{to:"/manager/warehouse_locations",text:"Locations",type:s.LINK,value:"warehouse location"},{to:"/manager/location_types",text:"Location Types",type:s.LINK,value:"location_type"},{to:"/manager/packaging_materials",text:"Packaging Materials",type:s.LINK,value:"packaging_materials"}],F=[{to:"/manager/dashboard",text:"Dashboard",type:s.LINK,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"dashboard"},{to:"/manager/orders?view=pending",text:"Orders",type:s.LINK,dropdownItems:K,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"orders"},{to:"/manager/receipts",text:"Receipts",type:s.LINK,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"receipts"},{to:"/manager/inventory",text:"Inventory",type:s.LINK,dropdownItems:k,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"inventory"},{to:"/manager/items",text:"Items",type:s.LINK,dropdownItems:w,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"items"},{to:"/manager/reports",text:"Reports",type:s.LINK,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"reports"},{to:"/manager/installation",text:"Installation",type:s.LINK,icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"}),value:"installation"},{to:"/manager/products",text:"Products",type:s.LINK,value:"products",icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"})},{to:"/manager/exhibitor_request?view=submitted",text:"Exhibitor Request",type:s.LINK,value:"exhibitor request",icon:e.jsx(l,{className:"text-xl text-[#A8A8A8]"})}],se=()=>{const{state:f,dispatch:c}=u.useContext(b);u.useContext(y);const[_,o]=m.useState(""),{isOpen:C,showBackButton:v,path:a}=f,x=d();return m.useEffect(()=>{const t=x.pathname.split("/");t[1]!=="user"&&t[1]!=="manager"?o(t[1]):o(t[2])},[x]),e.jsxs("div",{className:"sticky inset-x-0 top-0 z-20 m-auto flex h-fit max-h-fit min-h-[5.4375rem] w-full min-w-full max-w-full flex-col items-center justify-between bg-black px-6  pt-2 md:min-w-[auto] md:max-w-[auto]",children:[e.jsxs("div",{className:"flex w-full min-w-full max-w-full justify-between gap-10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[v&&e.jsx(A,{}),e.jsx("h1",{className:"text-xl capitalize",children:e.jsx(g,{})})]}),e.jsx(j,{children:e.jsx(L,{})})]}),e.jsx("div",{className:"scrollbar-hide w-full max-w-full overflow-x-auto md:overflow-x-clip",children:e.jsxs("ul",{className:"flex w-fit justify-start text-sm",children:[F.map(t=>{var i;switch(t.type){case s.LINK:return e.jsx("li",{className:`flex !w-fit !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2  ${a===t.value?"border-b-2 border-b-third text-white":"text-[#ffffff99]"}`,children:e.jsx(h,{to:t.to,className:`${a===t.value?"active-nav":""} !w-fit`,children:e.jsx("div",{className:"flex !w-fit items-center gap-3",children:e.jsx("span",{className:"!w-fit",children:t.text})})})},t.value);case s.DROPDOWN:return e.jsx(e.Fragment,{children:e.jsxs("li",{className:` relative flex cursor-pointer list-none items-center justify-center px-2 ${a===t.value?"border-b-2 border-b-third text-white":"text[#ffffff99"}`,children:[e.jsxs("button",{className:"peer flex h-fit cursor-pointer items-center gap-2 text-[1rem] ",children:[t.text,e.jsx("span",{className:"text-xs",children:"▽"})]}),e.jsx("ul",{className:"absolute top-[80%] z-20 hidden min-w-[12.5rem] rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-white shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block",children:t!=null&&t.dropdownItems&&((i=t.dropdownItems)!=null&&i.length)?t.dropdownItems.map((n,N)=>e.jsx("li",{className:"!w-fit",children:e.jsx(p,{className:`hover:text[#262626] flex !w-fit cursor-pointer items-center rounded-md px-4 py-3 text-black hover:bg-[#F4F4F4] ${a===n.value?"active-nav":""}`,to:n==null?void 0:n.to,children:e.jsx("span",{children:n.text})})},N)):null})]})})}}),r&&(r!=null&&r.length)?r.map((t,i)=>{switch(t==null?void 0:t.type){case s.LINK:return e.jsx("li",{className:`flex !w-fit  !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2 md:hidden  ${a===t.value?"border-b-2 border-b-third text-white":"text-white-600"}`,children:e.jsx(h,{to:t.to,className:`${a===t.value?"active-nav":""} !w-fit`,children:e.jsx("div",{className:"flex !w-fit items-center gap-3",children:e.jsx("span",{className:"!w-fit",children:t.text})})})},t.value);case s.ACTION:return e.jsx("li",{className:`flex !w-fit  !min-w-fit !max-w-fit items-center justify-center p-[0.75rem] px-2 md:hidden  ${a===t.value?"border-b-2 border-b-third text-white":"text-white-600"}`,children:e.jsx("button",{type:"button",onClick:()=>(t==null?void 0:t.action)&&t.action(c),className:`${a===t.value?"active-nav":""} !w-fit`,children:e.jsx("div",{className:"flex !w-fit items-center gap-3",children:e.jsx("span",{className:"!w-fit",children:t.text})})})},t.value)}}):null,e.jsxs("div",{className:"relative hidden cursor-pointer items-center md:flex",children:[e.jsxs("button",{className:"peer ml-3 flex cursor-pointer items-center text-sm text-white-600 ",children:["More",e.jsx("span",{className:"ml-2 text-xs",children:"▽"})]}),e.jsx("ul",{className:"absolute top-[80%] z-20 hidden min-w-[12.5rem] rounded-lg border border-[#a8a8a8] bg-white p-2 text-sm text-white shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block",children:r&&(r!=null&&r.length)?r.map((t,i)=>{switch(t.type){case s.LINK:return e.jsx("li",{className:"!w-fit !min-w-full",children:e.jsx(p,{className:`flex !w-full cursor-pointer items-center rounded-md px-4 py-3 text-black hover:bg-[#F4F4F4] hover:text-[#262626] ${a===t.value?"bg-[#F4F4F4]":""}`,to:t==null?void 0:t.to,children:e.jsx("span",{children:t.text})})},i);case s.ACTION:return e.jsx("li",{className:"!w-fit !min-w-full",children:e.jsx("button",{type:"button",onClick:()=>(t==null?void 0:t.action)&&t.action(c),className:`flex !w-full cursor-pointer items-center rounded-md px-4 py-3 text-black hover:bg-[#F4F4F4] hover:text-[#262626] ${a===t.value?"bg-[#F4F4F4]":""}`,children:e.jsx("span",{children:t.text})})},i)}}):null})]})]})})]})};export{se as default};
