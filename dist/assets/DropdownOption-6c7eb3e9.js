import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const n=({icon:s,onClick:t=()=>{},name:r,style:a={},className:o=""})=>e.jsxs("div",{style:{...a},className:`flex h-[2.25rem] max-h-[2.25rem] min-h-[2.25rem] w-full cursor-pointer items-center gap-3 px-2 capitalize text-[#262626] hover:bg-[#F4F4F4] hover:text-[#262626] ${o}`,onClick:i=>{t(i)},children:[s&&e.jsxs("span",{className:"",children:[" ",s]}),r&&e.jsxs("span",{className:"grow",children:[" ",r]})]});export{n as default};
