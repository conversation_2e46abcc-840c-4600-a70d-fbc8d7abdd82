import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r}from"./vendor-1c28ea83.js";import{_ as c}from"./MoonLoader-ca436abf.js";const l="_button_1e17q_1",p={button:l},x=({loading:o=!1,disabled:s,children:e,type:n="button",className:a,loaderclasses:f,onClick:i,color:m="#ffffff"})=>{const d={borderColor:"#ffffff"},u=r.useId();return t.jsx("button",{type:n,disabled:s,className:`${p.button} font-nter relative flex h-[3rem] cursor-pointer items-center justify-center gap-5 overflow-hidden rounded-[.625rem] border border-primary  bg-primary px-[.625rem] py-[.5625rem] text-sm font-medium leading-none text-white ${a}`,onClick:i,children:t.jsxs(t.Fragment,{children:[t.jsx(c,{color:m,loading:o,cssOverride:d,size:14,className:f,"data-testid":u}),e?t.jsx("span",{children:e}):null]})})},_=r.memo(x);export{_ as default};
