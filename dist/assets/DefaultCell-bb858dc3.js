import{j as t}from"./@react-google-maps/api-211df1ae.js";import{r as s,R as o}from"./vendor-1c28ea83.js";const p=s.memo(({value:r})=>{if(Array.isArray(r))return t.jsx("span",{children:r.join(", ")});if(typeof r=="string"&&r.startsWith("{")&&r.endsWith("}"))try{const e=JSON.parse(r);if(e&&typeof e=="object")return t.jsx("div",{className:"max-w-xs overflow-hidden text-ellipsis",children:Object.entries(e).filter(([i,n])=>n!=null&&i!=="created_at").map(([i,n])=>Array.isArray(n)?`${i}: ${n.join(", ")}`:typeof n=="object"?`${i}: [Object]`:`${i}: ${n}`).join(", ")})}catch(e){console.log("Failed to parse JSON:",e)}if(typeof r=="object"&&r!==null)try{return t.jsx("span",{children:JSON.stringify(r)})}catch(e){return console.log("Failed to stringify object:",e),t.jsx("span",{children:"[Complex Object]"})}return typeof r=="boolean"?t.jsx("span",{children:r?"Yes":"No"}):typeof r=="number"?t.jsx("span",{children:r}):t.jsx(t.Fragment,{children:o.isValidElement(r)?r:r==null?"-":String(r)})});export{p as default};
