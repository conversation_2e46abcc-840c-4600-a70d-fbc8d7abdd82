import{j as C}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const n=({className:e="",stroke:l="#2D9F75",fill:t="#7B1113",onClick:i=()=>{}})=>C.jsxs("svg",{className:`${e}`,onClick:i,width:"14",height:"12",viewBox:"0 0 14 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[C.jsx("path",{d:"M0.833496 0C0.557354 0 0.333496 0.223858 0.333496 0.5V2.16667C0.333496 2.44281 0.557354 2.66667 0.833496 2.66667H13.1668C13.443 2.66667 13.6668 2.44281 13.6668 2.16667V0.5C13.6668 0.223858 13.443 0 13.1668 0H0.833496Z",fill:l}),C.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.0002 3.66667H1.00016L1.00016 10.4508C1.00015 10.6225 1.00014 10.7801 1.01091 10.9119C1.02252 11.054 1.04909 11.2095 1.12732 11.363C1.23917 11.5825 1.41765 11.761 1.63717 11.8728C1.79071 11.9511 1.94616 11.9776 2.08825 11.9893C2.22004 12 2.37769 12 2.54934 12H11.451C11.6226 12 11.7803 12 11.9121 11.9893C12.0542 11.9776 12.2096 11.9511 12.3632 11.8728C12.5827 11.761 12.7612 11.5825 12.873 11.363C12.9512 11.2095 12.9778 11.054 12.9894 10.9119C13.0002 10.7801 13.0002 10.6225 13.0002 10.4508V3.66667ZM5.16683 5.83333C5.16683 5.55719 5.39069 5.33333 5.66683 5.33333H8.3335C8.60964 5.33333 8.8335 5.55719 8.8335 5.83333C8.8335 6.10948 8.60964 6.33333 8.3335 6.33333H5.66683C5.39069 6.33333 5.16683 6.10948 5.16683 5.83333Z",fill:l})]});export{n as default};
