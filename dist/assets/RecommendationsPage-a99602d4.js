import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as l}from"./vendor-1c28ea83.js";import{G as k,M as R,s as y}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const S=m=>new Date(m).toLocaleString(),X=()=>{const{dispatch:m}=l.useContext(k),[c,w]=l.useState([]),[C,p]=l.useState(!0),[f,j]=l.useState(""),[a,x]=l.useState(""),[g,u]=l.useState([]),[n,v]=l.useState("to-you");l.useEffect(()=>{N()},[n]),l.useEffect(()=>{if(a.trim()==="")u(c);else{const t=c.filter(s=>{var r,d,o,i,b;return((r=s.referral_title)==null?void 0:r.toLowerCase().includes(a.toLowerCase()))||((d=s.referral_description)==null?void 0:d.toLowerCase().includes(a.toLowerCase()))||((o=s.first_name)==null?void 0:o.toLowerCase().includes(a.toLowerCase()))||((i=s.last_name)==null?void 0:i.toLowerCase().includes(a.toLowerCase()))||((b=s.email)==null?void 0:b.toLowerCase().includes(a.toLowerCase()))||s.community_name&&s.community_name.toLowerCase().includes(a.toLowerCase())});u(t)}},[a,c]);const N=async()=>{try{p(!0);const t=new R,s=n==="to-you"?"/v1/api/dealmaker/user/my/recommendations":"/v1/api/dealmaker/user/recommendations",r=await t.callRawAPI(s,{},"GET");if(r.error)j(r.message),y(m,r.message||"Failed to load recommendations",5e3,"error");else{const o=(r.data||r.list||[]).sort((i,b)=>new Date(b.created_at)-new Date(i.created_at));w(o),u(o)}}catch(t){console.error("Failed to load recommendations:",t),j(t.message||"Failed to load recommendations"),y(m,t.message||"Failed to load recommendations",5e3,"error")}finally{p(!1)}},_=t=>{x(t.target.value)},h=c,L=t=>{switch(t){case"active":return"bg-green-600 text-white";case"completed":return"bg-blue-600 text-white";case"expired":return"bg-red-600 text-white";default:return"bg-gray-600 text-white"}};return f?e.jsx("div",{className:"flex min-h-screen items-center justify-center bg-black",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-bold text-red-500 mb-4",children:"Error"}),e.jsx("p",{className:"text-[#b5b5b5] mb-4",children:f}),e.jsx("button",{onClick:N,className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea] hover:bg-[#1b5e20]",children:"Try Again"})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-[#eaeaea]",children:"My Referrals"}),e.jsx("p",{className:"text-[#b5b5b5]",children:"View referrals referred to you and by you"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search referrals...",value:a,onChange:_,className:"h-[42px] w-64 rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea] placeholder-[#b5b5b5] focus:border-[#2e7d32] focus:outline-none"}),e.jsx("svg",{className:"absolute right-3 top-3 h-4 w-4 text-[#b5b5b5]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex space-x-1 rounded-lg bg-[#1e1e1e] p-1",children:[e.jsx("button",{onClick:()=>{v("to-you"),x("")},className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${n==="to-you"?"bg-[#2e7d32] text-white":"text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Referred to You"}),e.jsx("button",{onClick:()=>{v("by-you"),x("")},className:`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${n==="by-you"?"bg-[#2e7d32] text-white":"text-[#b5b5b5] hover:text-[#eaeaea]"}`,children:"Referred by You"})]})}),e.jsxs("div",{className:"mb-6 grid grid-cols-1 gap-4 sm:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-[#b5b5b5]",children:n==="to-you"?"Referred to You":"Referred by You"}),e.jsx("p",{className:"text-2xl font-bold text-[#eaeaea]",children:h.length})]}),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-[#b5b5b5]",children:"Active Opportunities"}),e.jsx("p",{className:"text-2xl font-bold text-[#eaeaea]",children:h.filter(t=>t.referral_status==="active").length})]}),e.jsxs("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-4",children:[e.jsx("h3",{className:"text-sm font-medium text-[#b5b5b5]",children:"Completed Opportunities"}),e.jsx("p",{className:"text-2xl font-bold text-[#eaeaea]",children:h.filter(t=>t.referral_status==="completed").length})]})]}),C?e.jsx("div",{className:"flex justify-center py-8",children:e.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-4 border-[#2e7d32] border-t-transparent"})}):g.length>0?e.jsx("div",{className:"space-y-4",children:g.map(t=>e.jsx("div",{className:"rounded-lg border border-[#363636] bg-[#1e1e1e] p-6 hover:border-[#2e7d32] transition-colors",children:e.jsx("div",{className:"flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2 mb-3",children:[e.jsx("h3",{className:"text-lg font-semibold text-[#eaeaea]",children:t.referral_title}),e.jsx("span",{className:`rounded-full px-2 py-1 text-xs font-medium ${L(t.referral_status)}`,children:t.referral_status})]}),e.jsx("p",{className:"text-[#b5b5b5] mb-4",children:t.referral_description}),e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsxs("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm font-medium text-white",children:[t.first_name.charAt(0),t.last_name.charAt(0)]}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm font-medium text-[#eaeaea]",children:[t.first_name," ",t.last_name]}),e.jsx("p",{className:"text-xs text-[#b5b5b5]",children:t.email})]})]}),t.community_name&&e.jsx("div",{className:"mb-3",children:e.jsxs("span",{className:"inline-flex items-center rounded-md bg-[#363636] px-2 py-1 text-xs text-[#eaeaea]",children:[e.jsx("svg",{className:"mr-1 h-3 w-3",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"})}),t.community_name]})}),t.recommendation_data&&t.recommendation_data.length>0&&e.jsxs("div",{className:"mb-3",children:[e.jsx("p",{className:"text-sm font-medium text-[#eaeaea] mb-2",children:"Referred Candidates:"}),e.jsx("div",{className:"space-y-1",children:t.recommendation_data.map((s,r)=>{var d,o,i;return e.jsxs("div",{className:"text-sm text-[#b5b5b5]",children:["• ",(d=s.first_name)==null?void 0:d.value," ",(o=s.last_name)==null?void 0:o.value," (",(i=s.email)==null?void 0:i.value,")"]},r)})})]}),e.jsxs("p",{className:"text-xs text-[#b5b5b5]",children:["Referred on ",S(t.created_at)]})]})})},t.id))}):e.jsxs("div",{className:"flex flex-col items-center justify-center py-12 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-[#363636] p-3",children:e.jsx("svg",{className:"h-8 w-8 text-[#b5b5b5]",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})}),e.jsx("h3",{className:"text-xl font-medium text-[#eaeaea] mb-2",children:"No referrals found"}),e.jsx("p",{className:"text-[#b5b5b5] mb-4",children:a?"No referrals match your search criteria.":n==="to-you"?"You haven't received any referrals yet.":"You haven't made any referrals yet."}),a&&e.jsx("button",{onClick:()=>x(""),className:"rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea] hover:bg-[#1b5e20] transition-colors",children:"Clear Search"})]})]})})};export{X as default};
