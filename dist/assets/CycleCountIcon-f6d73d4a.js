import{j as t}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";const o=({className:e="",fill:r="none",stroke:s="#717179",onClick:C=()=>{}})=>t.jsxs("svg",{className:`${e}`,onClick:C,width:"20",height:"20",viewBox:"0 0 20 20",fill:r,xmlns:"http://www.w3.org/2000/svg",children:[t.jsx("path",{d:"M12.5 4.79175C12.5 6.17246 11.3807 7.29175 10 7.29175C8.61929 7.29175 7.5 6.17246 7.5 4.79175C7.5 3.41104 8.61929 2.29175 10 2.29175C11.3807 2.29175 12.5 3.41104 12.5 4.79175Z",fill:"#717179",stroke:s,strokeWidth:"1.5",strokeLinecap:"square"}),t.jsx("path",{d:"M4.79199 7.5C6.1727 7.5 7.29199 8.61929 7.29199 10C7.29199 11.3807 6.1727 12.5 4.79199 12.5C3.41128 12.5 2.29199 11.3807 2.29199 10C2.29199 8.61929 3.41128 7.5 4.79199 7.5Z",stroke:s,strokeWidth:"1.5",strokeLinecap:"square"}),t.jsx("path",{d:"M15.2087 7.5C16.5894 7.5 17.7087 8.61929 17.7087 10C17.7087 11.3807 16.5894 12.5 15.2087 12.5C13.8279 12.5 12.7087 11.3807 12.7087 10C12.7087 8.61929 13.8279 7.5 15.2087 7.5Z",stroke:s,strokeWidth:"1.5",strokeLinecap:"square"}),t.jsx("path",{d:"M12.5003 15.2083C12.5003 16.589 11.381 17.7083 10.0003 17.7083C8.61961 17.7083 7.50033 16.589 7.50033 15.2083C7.50033 13.8276 8.61961 12.7083 10.0003 12.7083C11.381 12.7083 12.5003 13.8276 12.5003 15.2083Z",stroke:s,strokeWidth:"1.5",strokeLinecap:"square"})]});export{o as default};
