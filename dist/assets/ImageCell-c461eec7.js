import{j as r}from"./@react-google-maps/api-211df1ae.js";import{L as o}from"./index-826b1c0e.js";import{M as e}from"./index-dbfe2d0c.js";import{r as i}from"./vendor-1c28ea83.js";import{c as p}from"./index.esm-1ac45320.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const v=i.memo(({src:t,onPopoverStateChange:m})=>r.jsx(o,{children:r.jsx(e,{display:r.jsx(p,{className:"peer h-8 w-8"}),openOnClick:!1,zIndex:999999999999999,onPopoverStateChange:m,place:"left-start",tooltipClasses:"whitespace-nowrap h-fit min-h-[1rem] max-h-fit w-[18.75rem] !rounded-lg border border-[#a8a8a8] !bg-white p-2 text-sm text-[#525252] shadow-md",children:r.jsx(o,{className:"h-[18.75rem] w-[18.75rem] whitespace-nowrap !rounded-lg border border-[#a8a8a8] !bg-white p-2 text-sm text-[#525252] shadow-md",children:r.jsx("img",{src:t,className:"w-[18.75rem]",alt:""})})})}));export{v as default};
