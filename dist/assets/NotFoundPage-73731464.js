import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as o,R as t}from"./vendor-1c28ea83.js";import{_ as a}from"./qr-scanner-cf010ec4.js";const i=o.lazy(()=>a(()=>import("./Loader-444291bb.js"),["assets/Loader-444291bb.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/qr-scanner-cf010ec4.js"])),m=()=>{const[r,s]=t.useState(!0);return t.useEffect(()=>{setTimeout(()=>{s(!1)},5e3)},[]),e.jsx(e.Fragment,{children:r?e.jsx(i,{}):e.jsx("div",{className:"flex h-screen w-full items-center justify-center text-7xl text-gray-700 ",children:"Not Found"})})};export{m as default};
