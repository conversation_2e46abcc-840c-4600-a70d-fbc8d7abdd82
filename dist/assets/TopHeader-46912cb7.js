import{j as e}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{a3 as r}from"./index-826b1c0e.js";import"./index-82d4da4e.js";import"./pdf-lib-623decea.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const y=()=>{const{profile:t}=r();return e.jsx("header",{className:"fixed left-0 right-0 top-0 z-30 h-14 border-b border-[#363636] bg-[#161616] pl-64",children:e.jsxs("div",{className:"flex h-full items-center justify-between px-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{className:"h-6 w-6",alt:"Logo",src:"/logo.svg"}),e.jsx("span",{className:"text-xl font-bold text-[#eaeaea]",children:"Rain Maker"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"text-[#eaeaea] hover:text-[#7dd87d]",children:t==null?void 0:t.name}),e.jsx("button",{className:"text-[#eaeaea] hover:text-red-500",children:"Logout"})]})]})})};export{y as default};
