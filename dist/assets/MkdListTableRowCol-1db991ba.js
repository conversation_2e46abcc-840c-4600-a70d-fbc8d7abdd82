import{j as r}from"./@react-google-maps/api-211df1ae.js";import{r as c}from"./vendor-1c28ea83.js";import{_ as a}from"./qr-scanner-cf010ec4.js";const F=c.lazy(()=>a(()=>import("./CurrencyCell-eef801cc.js"),["assets/CurrencyCell-eef801cc.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"])),O=c.lazy(()=>a(()=>import("./DefaultCell-bb858dc3.js"),["assets/DefaultCell-bb858dc3.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"])),w=c.lazy(()=>a(()=>import("./EditableStatusCell-6967a016.js"),["assets/EditableStatusCell-6967a016.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"])),N=c.lazy(()=>a(()=>import("./EditableTextCell-2ecce261.js"),["assets/EditableTextCell-2ecce261.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"])),J=c.lazy(()=>a(()=>import("./FileCell-262a832b.js"),["assets/FileCell-262a832b.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-dbfe2d0c.js"])),S=c.lazy(()=>a(()=>import("./ImageCell-c461eec7.js"),["assets/ImageCell-c461eec7.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-dbfe2d0c.js","assets/index.esm-1ac45320.js"])),k=c.lazy(()=>a(()=>import("./JoinCell-b95f223e.js"),["assets/JoinCell-b95f223e.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"])),H=c.lazy(()=>a(()=>import("./ListCell-973c0a09.js"),["assets/ListCell-973c0a09.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-53bbb473.js","assets/qr-scanner-cf010ec4.js"])),q=c.lazy(()=>a(()=>import("./NoteCell-bd6ab19b.js"),["assets/NoteCell-bd6ab19b.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),P=c.lazy(()=>a(()=>import("./StatusCell-8dc9b230.js"),["assets/StatusCell-8dc9b230.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/MkdPopover-c3c82be8.js","assets/react-tooltip-ad20e3af.js","assets/@mantine/core-76ed7ee7.js","assets/@uppy/dashboard-3a4b1704.js","assets/@craftjs/core-a5d68af1.js","assets/MkdPopover-e3b0c442.css","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css"])),b=c.lazy(()=>a(()=>import("./TruncatedCell-a176787c.js"),["assets/TruncatedCell-a176787c.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js"])),B=i=>{const p={year:"numeric",month:"long",day:"numeric"},E=new Date(i),[y,g]=i.split("T"),[f,j]=[E.getHours(),E.getMinutes()];return g&&(f||j)&&(p.hour="2-digit",p.minute="2-digit"),new Date(i).toLocaleDateString(void 0,p)},G=({column:i,tableRole:p,handleTableCellChange:E,allowEditing:y,onPopoverStateChange:g,row:f,currentTableData:j,expandRow:h,showNote:V})=>{const t=c.useMemo(()=>i,[i]),n=c.useMemo(()=>f,[f]),M=c.useMemo(()=>j,[j]),$=()=>{var v,D,T,A,L,R,I;let s=n[t==null?void 0:t.accessor];if((t==null?void 0:t.accessor)==="data"&&typeof s=="string")try{const _=JSON.parse(s);t!=null&&t.dataField&&_?(s=_[t.dataField],t.dataField==="user_type"&&Array.isArray(s)&&(s=s.join(", "))):s=Object.entries(_).filter(([e,x])=>x!=null&&e!=="created_at").map(([e,x])=>Array.isArray(x)?`${e}: ${x.join(", ")}`:typeof x=="object"?`${e}: [Object]`:`${e}: ${x}`).join(", ")}catch{}return s==null?r.jsx(O,{value:"-"}):(((v=t==null?void 0:t.accessor)==null?void 0:v.indexOf("image"))>-1||((D=t==null?void 0:t.accessor)==null?void 0:D.indexOf("photo"))>-1)&&(t!=null&&t.selected_column)&&s?r.jsx(S,{src:s,onPopoverStateChange:g}):(((T=t==null?void 0:t.accessor)==null?void 0:T.indexOf("pdf"))>-1||((A=t==null?void 0:t.accessor)==null?void 0:A.indexOf("doc"))>-1||((L=t==null?void 0:t.accessor)==null?void 0:L.indexOf("file"))>-1||((R=t==null?void 0:t.accessor)==null?void 0:R.indexOf("video"))>-1||["attached_files","attached_file"].includes(t==null?void 0:t.accessor))&&(t!=null&&t.selected_column)&&s?r.jsx(J,{value:s}):t!=null&&t.join&&(t!=null&&t.selected_column)?r.jsx(k,{value:(I=n[t==null?void 0:t.join])==null?void 0:I[t==null?void 0:t.accessor]}):t!=null&&t.mappingExist&&!["admin"].includes(p)&&(t!=null&&t.selected_column)?r.jsx(P,{value:s,mappings:t==null?void 0:t.mappings}):t!=null&&t.mappingExist&&["admin"].includes(p)&&(t!=null&&t.selected_column)?r.jsx(P,{value:s,mappings:t==null?void 0:t.mappings}):t!=null&&t.mappingExist&&y&&["admin"].includes(p)&&(t!=null&&t.selected_column)?r.jsx(w,{value:s,mappings:t==null?void 0:t.mappings,onChange:_=>E(_.target.value,t==null?void 0:t.accessor,n.id)}):!(t!=null&&t.mappingExist)&&(t==null?void 0:t.accessor)!=="id"&&(t==null?void 0:t.accessor)!=="create_at"&&(t==null?void 0:t.accessor)!=="update_at"&&(t==null?void 0:t.accessor)!=="user_id"&&(t==null?void 0:t.accessor)!==""&&y&&(t!=null&&t.selected_column)?r.jsx(N,{value:s,onChange:_=>E(_.target.value,t==null?void 0:t.accessor,n.id)}):t!=null&&t.truncate&&(t!=null&&t.selected_column)?r.jsx(b,{value:s,length:50}):t!=null&&t.replace&&(t!=null&&t.selected_column)?r.jsx(b,{value:s,length:30}):t!=null&&t.list&&(t!=null&&t.selected_column)?r.jsx(H,{column:t,data:s,expandRow:h,currentTableData:M}):t!=null&&t.isCurrency&&(t!=null&&t.selected_column)?r.jsx(F,{currency:t==null?void 0:t.currency,value:s}):["notes","note"].includes(t==null?void 0:t.accessor)&&(t!=null&&t.selected_column)&&s?r.jsx(q,{value:s,showNote:V}):["update_at","create_at"].includes(t==null?void 0:t.accessor)&&(t!=null&&t.selected_column)?r.jsx(O,{value:B(s)}):(t==null?void 0:t.accessor)!==""?r.jsx(O,{value:s}):null};return r.jsx("td",{style:{background:(f==null?void 0:f.bg)??""},className:"!w-[auto]  !min-w-[6.25rem] !max-w-[300px]  !truncate whitespace-nowrap  border-b border-b-gray-200 px-6",children:$()})},W=c.memo(G);export{W as default};
