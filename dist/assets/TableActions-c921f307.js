import{j as p}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{b as e}from"./index-826b1c0e.js";import{A as g}from"./index-e2604cb4.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const H=({actions:i,selectedItems:o})=>p.jsx("div",{className:"flex gap-2",children:Object.keys(i).map(r=>i[r].show).includes(!0)?p.jsx(p.Fragment,{children:Object.keys(i).map(r=>{var t,a,n,m,d,u,f,x;if(i[r].show&&!["static"].includes(i[r].type)&&!["select","add","export"].includes(r)){if(o&&(o==null?void 0:o.length)===1&&!((t=i[r])!=null&&t.multiple))return p.jsx(g,{showPlus:!1,loading:((a=i[r])==null?void 0:a.loading)??!1,disabled:((n=i[r])==null?void 0:n.disabled)??!1,icon:((m=i[r])==null?void 0:m.icon)??null,className:`!h-[2.5rem] cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var l;(l=i[r])!=null&&l.action&&i[r].action(o)},children:e(r,{casetype:"capitalize",separator:" "})},r);if(o&&(o==null?void 0:o.length)>=1&&((d=i[r])!=null&&d.multiple))return p.jsx(g,{showPlus:!1,loading:((u=i[r])==null?void 0:u.loading)??!1,disabled:((f=i[r])==null?void 0:f.disabled)??!1,icon:((x=i[r])==null?void 0:x.icon)??null,className:`!h-[2.5rem] cursor-pointer px-2 py-2 text-lg  font-medium leading-loose tracking-wide ${r==="view"?"text-blue-500":r==="delete"?"text-red-500":"text-[#292829fd]"} hover:underline`,onClick:()=>{var l;(l=i[r])!=null&&l.action&&i[r].action(o)},children:e(r,{casetype:"capitalize",separator:" "})},r)}}).filter(Boolean)}):null});export{H as default};
