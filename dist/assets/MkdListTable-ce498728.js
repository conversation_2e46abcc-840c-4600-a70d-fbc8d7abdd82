import{j as l}from"./@react-google-maps/api-211df1ae.js";import{r as m,R as w,d as je}from"./vendor-1c28ea83.js";import{A as Ne,G as ve,L as c,S as ke,ag as Se,ah as Me}from"./index-826b1c0e.js";import ze from"./MkdListTableRowCol-1db991ba.js";import"./pdf-lib-623decea.js";import"./index-23518dfb.js";import"./index-dbfe2d0c.js";import"./index-6bbb3077.js";import"./index-e2604cb4.js";import{_ as Le}from"./qr-scanner-cf010ec4.js";import{a as Re,b as _e}from"./index-53bbb473.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const Oe="/assets/no_data_found-a6b77ebc.png",$e=m.lazy(()=>Le(()=>import("./NoteModal-3e19d1ee.js"),["assets/NoteModal-3e19d1ee.js","assets/@react-google-maps/api-211df1ae.js","assets/vendor-1c28ea83.js","assets/index-826b1c0e.js","assets/react-confirm-alert-143e7b1b.js","assets/qr-scanner-cf010ec4.js","assets/pdf-lib-623decea.js","assets/@headlessui/react-0d33a5d7.js","assets/react-quill-4ec0fa7c.js","assets/@craftjs/core-a5d68af1.js","assets/react-hook-form-eec8b32f.js","assets/@hookform/resolvers-2530002b.js","assets/yup-f303108c.js","assets/react-icons-5238c8a8.js","assets/country-state-city-7cb9a309.js","assets/@stripe/stripe-js-6b714a86.js","assets/@stripe/react-stripe-js-69862bfd.js","assets/@fortawesome/react-fontawesome-b5aa048d.js","assets/react-pdf-b254e02e.js","assets/@react-pdf-viewer/core-b4c6819d.js","assets/react-calendar-eace60fa.js","assets/index-b29464e7.css","assets/inter-696bfa8d.css","assets/poppins-7d4f40ab.css","assets/index-632d14e3.js","assets/index-23518dfb.js","assets/index-e2604cb4.js"])),Ce=({table:Ae,tableTitle:Ee,onSort:P,loading:_,setLoading:be,columns:t=[],actions:s,actionPostion:j=[],tableRole:I,deleteItem:Fe,deleteLoading:Ge,actionId:N="id",showDeleteModal:Je,currentTableData:h=[],setShowDeleteModal:T,handleTableCellChange:a,setSelectedItems:u,allowEditing:D,useImage:O=!0,columnData:i=null,setColumns:Ke=null,setColumnData:b=null,selectedItems:o=[],allowSortColumns:y=!0,onPopoverStateChange:ee=null,popoverShown:Be=!1,maxHeight:He=null})=>{var H,V,W,q,Q,U,X,Y,Z;console.log(t,"columnsd"),console.log(i,"columnData"),console.log(h,"currentTableData"),w.useContext(Ne),w.useContext(ve);const[Ve,re]=w.useState(null),[We,se]=w.useState(!1),[qe,$]=w.useState(!1),[ie,C]=w.useState(!1),[M,A]=w.useState(null),[z,L]=w.useState(null);w.useState(0);const[le,F]=w.useState(null),[he,G]=w.useState(null),[ne,Qe]=w.useState({key:null,held:!1,startingKey:!1}),fe=e=>{F(e),G(!0)},de=m.useMemo(()=>JSON.stringify(h),[h]);m.useMemo(()=>JSON.stringify((i==null?void 0:i.columns)||t),[i,t]);const R=m.useMemo(()=>{var e;return(e=(i==null?void 0:i.columns)||t)==null?void 0:e.find(r=>(r==null?void 0:r.accessor)==="row")},[i,t]),J=m.useMemo(()=>{var r,f;const e=(i==null?void 0:i.columns)||t;return(e==null?void 0:e.find(n=>(n==null?void 0:n.accessor)===""))&&((r=s==null?void 0:s.delete)==null?void 0:r.show)||((f=Object.keys(s).filter(n=>{var d,p,x,g,v,E,S,k;return((d=s[n])==null?void 0:d.show)&&((p=s[n])==null?void 0:p.locations)&&((g=(x=s[n])==null?void 0:x.locations)==null?void 0:g.length)&&(((E=(v=s[n])==null?void 0:v.locations)==null?void 0:E.includes("dropdown"))||((k=(S=s[n])==null?void 0:S.locations)==null?void 0:k.includes("buttons")))}))==null?void 0:f.length)},[t,i,s]),oe=m.useCallback(e=>e.length>1?h.findIndex(f=>(f==null?void 0:f.id)==(e==null?void 0:e[(e==null?void 0:e.length)-1])):e.length==1?h.findIndex(f=>(f==null?void 0:f.id)==(e==null?void 0:e[0])):null,[h]),te=m.useCallback((e,r)=>{const f=e<r?e:r,n=e>r?e:r,d=f==r?f+1:f,p=n==r?n-1:n;return{start:d,end:p,lower:f,upper:n}},[]),ge=m.useCallback((e,r,f)=>{var d,p;const n=oe(f);if(n!==null){const{lower:x,upper:g,start:v,end:E}=te(r,n),S=[...o];for(let k=x;k<=g;k++)S.push((d=h[k])==null?void 0:d.id);u(S)}else u([(p=h==null?void 0:h[r])==null?void 0:p.id])},[ne,h]);function K(e,r,f){var d,p;const n=o;if((d=s==null?void 0:s.select)!=null&&d.multiple)if((p=f==null?void 0:f.nativeEvent)!=null&&p.shiftKey)ge(e,r,n);else if(n.includes(e)){const x=n.filter(g=>g!==e);u(x)}else{const x=[...n,e];u(x)}else if(n.includes(e)){const x=n.filter(g=>g!==e);u(x)}else u([e])}const xe=()=>{if(h.every(r=>o.includes(r==null?void 0:r.id)))u([]);else{const r=h.map(f=>f[N]);u(r)}};je();const B=async e=>{T(!0),re(e)},pe=(e,r)=>{y&&(A(r),C(!0))},we=e=>{if(y){if(e.preventDefault(),M&&z&&M!=z&&(i!=null&&i.columns))try{const r=[...i.columns],f=r[M];r.splice(M,1),r.splice(z,0,f),b&&b(n=>({...n,columns:r}))}catch(r){console.error("Error in onDrop:",r)}L(null),A(null),C(!1)}},me=(e,r)=>{y&&(e.preventDefault(),L(r))},ue=e=>{y&&(e.preventDefault(),L(null),A(null),C(!1))},ye=e=>{y&&(e.preventDefault(),L(null))};return w.useEffect(()=>{o.length<=0&&(se(!1),$(!1)),o.length===(h==null?void 0:h.length)&&$(!0),o.length<(h==null?void 0:h.length)&&o.length>0&&$(!1)},[o==null?void 0:o.length,de]),l.jsx(c,{count:7,counts:[2,2,2,2,2,2],children:l.jsxs("div",{className:"relative !h-full !max-h-full !min-h-full  !w-full min-w-full max-w-full justify-center overflow-auto !rounded-[.625rem] border border-gray-900",children:[_&&O||_&&(!(h!=null&&h.length)||!((H=i==null?void 0:i.columns)!=null&&H.length)&&!(t!=null&&t.length))?l.jsx("div",{className:"max-h-fit min-h-fit w-full items-center justify-center",children:l.jsx(ke,{count:7,counts:[2,2,2,2,2,2]})}):h!=null&&h.length&&((V=i==null?void 0:i.columns)!=null&&V.length||t!=null&&t.length)?l.jsxs("table",{className:"h-fit min-w-full table-auto border-collapse divide-y divide-gray-800 rounded-md",children:[l.jsx("thead",{className:"bg-gray-800",children:l.jsxs("tr",{className:"!h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem]",children:[[(W=s==null?void 0:s.select)==null?void 0:W.show].includes(!0)||R?l.jsxs(l.Fragment,{children:[[(q=s==null?void 0:s.select)==null?void 0:q.show].includes(!0)?l.jsx("th",{className:"$ sticky -left-[0.05rem] -top-[0.05rem] z-[19] !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] !max-w-[2.65rem] bg-gray-100 px-[.75rem] py-[.5rem] text-xs font-medium capitalize tracking-wider text-gray-500",children:(Q=s==null?void 0:s.select)!=null&&Q.multiple?l.jsx("input",{type:"checkbox",disabled:!((U=s==null?void 0:s.select)!=null&&U.multiple),id:"select_all_rows",className:" focus:shadow-outline mr-1 !h-4 !w-4 cursor-pointer  rounded border border-gray-800 leading-tight text-primary accent-primary shadow focus:outline-none focus:ring-0",checked:(o==null?void 0:o.length)===(h==null?void 0:h.length),onChange:xe}):null}):null,R?l.jsx("th",{className:`$ sticky -top-[0.05rem] ${[(X=s==null?void 0:s.select)==null?void 0:X.show].includes(!0)?"left-10":"left-0"} z-10 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] max-w-[auto] bg-gray-100 px-[.75rem] py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500`,children:"Row"}):null]}):null,(Z=((Y=i==null?void 0:i.columns)==null?void 0:Y.length)>0?i.columns:t)==null?void 0:Z.map((e,r)=>{var f,n;return["row",""].includes(e==null?void 0:e.accessor)?null:l.jsx("th",{draggable:y,onDragStart:d=>pe(d,r),onDragEnd:ue,onDragOver:d=>me(d,r),onDragLeave:d=>ye(d),onDrop:d=>we(d),scope:"col",className:`$ sticky -top-[0.05rem] z-[5] !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[auto]  !min-w-[6.25rem] !max-w-[auto] shrink-0 grow px-6 py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500 ${y&&ie?"cursor-grabbing":e!=null&&e.isSorted?"cursor-pointer":""} ${z==r?"bg-primary-light":"bg-weak-100"} `,children:l.jsxs("div",{className:"flex w-full items-center justify-between gap-5",children:[l.jsxs("div",{className:"flex grow items-center justify-between gap-5",onClick:e!=null&&e.isSorted?()=>P(r):void 0,children:[l.jsx("div",{className:"w-auto grow whitespace-nowrap capitalize",children:(n=(f=e==null?void 0:e.header)==null?void 0:f.split("_"))==null?void 0:n.join(" ")}),l.jsx("span",{className:"w-fit",children:e.isSorted?l.jsx(Se,{className:`h-2 w-2 ${e.isSortedDesc?"rotate-180":""}`}):""})]}),y?l.jsx(Me,{className:"h-2 w-2 min-w-2 max-w-2 cursor-grab"}):null]})},r)}),J?l.jsx("th",{className:"$ sticky -right-[0.05rem] -top-[0.05rem] z-10 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-fit !min-w-fit max-w-fit shrink-0 grow bg-gray-100 px-[.75rem] py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500"}):null]})}),l.jsx("tbody",{className:"divide-y divide-gray-800 bg-gray-800",children:h==null?void 0:h.map((e,r)=>{var f,n,d,p,x;return l.jsxs("tr",{className:"!h-[3rem] !max-h-[3rem] !min-h-[3rem] border-b",children:[[(f=s==null?void 0:s.select)==null?void 0:f.show].includes(!0)||R?l.jsxs(l.Fragment,{children:[[(n=s==null?void 0:s.select)==null?void 0:n.show].includes(!0)?l.jsx("td",{className:"sticky -left-[0.05rem] z-10 !h-full !max-h-full !min-h-full !w-[2.65rem]  !min-w-[2.65rem] !max-w-[2.65rem] cursor-pointer whitespace-nowrap bg-gray-800 px-[.75rem] py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider text-sub-500",children:l.jsx("input",{type:"checkbox",className:" focus:shadow-outline mr-1 !h-4 !w-4 cursor-pointer  rounded leading-tight text-primary accent-primary shadow focus:outline-none focus:ring-0",name:"select_item",checked:(o==null?void 0:o.length)&&o.includes(e[N]),onChange:g=>K(e[N],r,g)})}):null,R?l.jsx("td",{className:`sticky ${[(d=s==null?void 0:s.select)==null?void 0:d.show].includes(!0)?"left-10":"left-0"} z-[5] flex h-full w-[auto] !min-w-[2.65rem] !max-w-[auto] items-center whitespace-nowrap border-b border-gray-800 bg-gray-800 px-[.75rem] py-[.5rem] text-sm`,children:r+1}):null]}):null,(x=((p=i==null?void 0:i.columns)==null?void 0:p.length)>0?i.columns:t)==null?void 0:x.map((g,v)=>["row",""].includes(g==null?void 0:g.accessor)?null:l.jsx(ze,{columnIndex:v,row:e,columns:(i==null?void 0:i.columns)||t,column:g,currentTableData:h,actions:s,allowEditing:D,handleSelectRow:K,handleTableCellChange:a,actionPostion:j,onPopoverStateChange:ee,selectedIds:o,actionId:N,tableRole:I,showNote:fe},v)),J?l.jsx("td",{className:"sticky -right-[0.05rem] z-[5] !w-fit !min-w-fit !max-w-fit whitespace-nowrap border-b border-b-gray-800  bg-gray-700 px-[.75rem] py-[.5rem]",children:l.jsxs("div",{className:"flex !w-fit !min-w-fit !max-w-fit items-center justify-end",children:[j!=null&&j.includes("dropdown")?l.jsx(Re,{row:e,actions:s,actionId:N,setDeleteId:B,columns:i==null?void 0:i.columns}):null,j!=null&&j.includes("buttons")?l.jsx(_e,{row:e,actions:s,actionId:N,setDeleteId:B,columns:i==null?void 0:i.columns}):null]})}):null]},r)})})]}):!_&&!(h!=null&&h.length)?l.jsx("div",{className:"relative w-full",children:l.jsx("div",{className:"relative max-h-fit min-h-fit w-full min-w-fit max-w-full items-center justify-center",children:l.jsx("div",{className:`relative ${O?"h-[35rem]":"flex h-[6.25rem] items-center justify-center"} w-full`,children:O?l.jsx("img",{src:Oe,className:"no-data-found absolute inset-x-0 m-auto h-full w-[50%] grayscale-[10%]"}):l.jsx(l.Fragment,{children:"No Data"})})})}):null,l.jsx(c,{children:l.jsx($e,{isOpen:he,note:le,onClose:()=>{G(!1),F(null)}})})]})})},jr=m.memo(Ce);export{jr as default};
