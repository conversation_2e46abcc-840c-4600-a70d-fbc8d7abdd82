import{j as o}from"./@react-google-maps/api-211df1ae.js";import"./vendor-1c28ea83.js";import{L as s,au as a}from"./index-826b1c0e.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-f303108c.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-b254e02e.js";import"./@react-pdf-viewer/core-b4c6819d.js";import"./react-calendar-eace60fa.js";const K=({icon:t,children:r,childrenWrapperClass:e="",iconWrapperClass:i="",className:m="",style:p={}})=>o.jsxs("div",{style:{...p},className:`relative flex items-center justify-center ${m}`,children:[o.jsx(s,{children:o.jsx("button",{className:`peer relative ${i}`,children:t||o.jsx(a,{})})}),o.jsx("div",{className:`absolute right-0 top-[85%] z-[9999999999] m-auto hidden  whitespace-nowrap rounded-lg border border-[#a8a8a8] bg-white text-sm text-[#525252] shadow-md hover:block focus:block peer-focus:block peer-focus-visible:block ${e} `,children:r})]});export{K as default};
